package org.springblade.websocket.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.dengliming.redismodule.redisearch.RediSearch;
import io.github.dengliming.redismodule.redisearch.client.RediSearchClient;
import io.github.dengliming.redismodule.redisearch.index.Document;
import io.github.dengliming.redismodule.redisearch.search.SearchOptions;
import io.github.dengliming.redismodule.redisearch.search.SearchResult;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DeviceStatusConstant;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.common.constant.PermissionType;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.LambdaUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springblade.entity.Location;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.websocket.constant.NodeType;
import org.springblade.websocket.constant.TreeCacheKeyConstant;
import org.springblade.websocket.dto.*;
import org.springblade.websocket.dto.tree.*;
import org.springblade.websocket.event.TreeWebsocketListener;
import org.springblade.websocket.mapper.TreeMapper;
import org.springblade.websocket.service.IBdmAbstractDeviceService;
import org.springblade.websocket.service.TreeService;
import org.springblade.websocket.vo.tree.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;


@Slf4j
@Service
public class TreeServiceImpl implements TreeService {


	@Resource
	private TreeMapper treeMapper;
	@Resource
	private ISysClient sysClient;

	@Resource
	private IBdmAbstractDeviceService deviceService;
	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	@Qualifier("treeRedisTemplate")
	private RedisTemplate<String, String> treeRedisTemplate;
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private TreeWebsocketListener treeWebsocketListener;

	@Resource
	private ObjectMapper objectMapper;
	@Resource
	private RedisScript<Long> clearKeysByPatternScript;
	@Resource
	private RedisScript<String> getTreeByCreatorScript;
	@Resource
	private RedisScript<String> getDeptTreeWithExpandedNodesScript;
	@Resource
	private CETokenUtil ceTokenUtil;

	@Resource
	private RediSearchClient rediSearchClient;

	@Resource
	private RedisScript<Long> updateDeviceStatusScript;


	@Resource
	private RedissonClient redissonClient;

	@Value("${acc-show.models}")
	private Set<String> models;


	/**
	 * 版本信息封装类
	 */
	@Getter
	private static final class VersionInfo {
		private final String currentVersion;
		private final String newVersion;

		public VersionInfo(String currentVersion, String newVersion) {
			this.currentVersion = currentVersion;
			this.newVersion = newVersion;
		}

	}

	/**
	 * 树构建数据封装类
	 */
	@Getter
	private static final class TreeBuildData {
		private final List<Dept> deptList;
		private final List<TargetDeviceTreeDto> deviceList;
		private final Map<String, String> typeNameMap;

		public TreeBuildData(List<Dept> deptList, List<TargetDeviceTreeDto> deviceList, Map<String, String> typeNameMap) {
			this.deptList = deptList;
			this.deviceList = deviceList;
			this.typeNameMap = typeNameMap;
		}

	}

	/**
	 * 树构建异常
	 */
	private static class TreeBuildException extends RuntimeException {
		public TreeBuildException(String message) {
			super(message);
		}

		public TreeBuildException(String message, Throwable cause) {
			super(message, cause);
		}

		public TreeBuildException(String message, String details) {
			super(message + ": " + details);
		}
	}

	/**
	 * 版本管理相关方法
	 */

	/**
	 * 获取当前活跃版本号
	 *
	 * @return 当前版本号，如果不存在则返回默认版本
	 */
	private String getCurrentVersion() {
		try {
			String currentVersion = treeRedisTemplate.opsForValue().get(TreeCacheKeyConstant.CURRENT_VERSION_KEY);
			return currentVersion != null ? currentVersion : TreeCacheKeyConstant.DEFAULT_VERSION;
		} catch (Exception e) {
			log.warn("获取当前版本失败，使用默认版本: {}", TreeCacheKeyConstant.DEFAULT_VERSION, e);
			return TreeCacheKeyConstant.DEFAULT_VERSION;
		}
	}

	private String getCurrentVersion(String newVersion) {
		if (newVersion == null) {
			return getCurrentVersion();
		}
		return newVersion;
	}

	/**
	 * 生成新版本号
	 *
	 * @param currentVersion 当前版本号
	 * @return 新版本号
	 */
	private String generateNewVersion(String currentVersion) {
		try {
			// 从版本号中提取数字部分，如 "v1" -> 1
			String numberPart = currentVersion.substring(TreeCacheKeyConstant.VERSION_PREFIX.length());
			int versionNumber = Integer.parseInt(numberPart);
			return TreeCacheKeyConstant.VERSION_PREFIX + (versionNumber + 1);
		} catch (Exception e) {
			log.warn("解析版本号失败: {}, 使用默认递增逻辑", currentVersion, e);
			// 如果解析失败，使用时间戳作为版本号
			return TreeCacheKeyConstant.VERSION_PREFIX + System.currentTimeMillis();
		}
	}

	/**
	 * 原子性地切换到新版本
	 *
	 * @param newVersion 新版本号
	 * @return 切换是否成功
	 */
	private boolean switchToNewVersion(String newVersion) {
		try {
			treeRedisTemplate.opsForValue().set(TreeCacheKeyConstant.CURRENT_VERSION_KEY, newVersion);
			log.info("成功切换到新版本: {}", newVersion);
			return true;
		} catch (Exception e) {
			log.error("切换到新版本失败: {}", newVersion, e);
			return false;
		}
	}

	/**
	 * 异步清理旧版本数据
	 *
	 * @param oldVersion 旧版本号
	 */
	private void cleanupOldVersionAsync(String oldVersion) {
		try {
			log.info("开始清理旧版本数据: {}", oldVersion);

			// 清理旧版本的各种key
			long deletedNodes = clearKeysByPattern(TreeCacheKeyConstant.getVersionedNodePattern(oldVersion));
			long deletedChildren = clearKeysByPattern(TreeCacheKeyConstant.getVersionedChildrenPattern(oldVersion));
			long deletedUserDevices = clearKeysByPattern(TreeCacheKeyConstant.getVersionedUserDevicesPattern(oldVersion));
			long deletedDeptDevices = clearKeysByPattern(TreeCacheKeyConstant.getVersionedDeptDevicesPattern(oldVersion));

			long totalDeleted = deletedNodes + deletedChildren + deletedUserDevices + deletedDeptDevices;
			log.info("旧版本 {} 清理完成，共删除 {} 个key (节点:{}, 子节点:{}, 用户终端:{}, 部门终端:{})",
				oldVersion, totalDeleted, deletedNodes, deletedChildren, deletedUserDevices, deletedDeptDevices);


		} catch (Exception e) {
			log.error("清理旧版本数据失败: {}", oldVersion, e);
		}
	}


	@Override
	public void buildTree(long lockSecond) {
		String currentVersion = null;
		String newVersion = null;

		RLock lock = redissonClient.getLock(TreeCacheKeyConstant.BUILD_TREE_LOCK_KEY);
		boolean lockAcquired = false;
		try {
			// 1. 获取分布式锁
			lockAcquired = lock.tryLock(lockSecond, TimeUnit.SECONDS);
			if (!lockAcquired) {
				log.info("获取缓存构建锁失败，已有其他任务正在执行。");
				return;
			}

			// 2. 设置构建状态
			setBuildingStatus(true);

			// 3. 准备版本信息
			VersionInfo versionInfo = prepareVersionInfo();
			currentVersion = versionInfo.getCurrentVersion();
			newVersion = versionInfo.getNewVersion();

			// 清理可能存在未完构建
			cleanupOldVersionAsync(newVersion);

			log.info("开始版本化构建 - 当前版本: {}, 新版本: {}", currentVersion, newVersion);

			// 5. 获取源数据
			TreeBuildData sourceData = fetchSourceData();
			log.info("获取到 {} 个部门，{} 个终端",
				sourceData.getDeptList().size(), sourceData.getDeviceList().size());

			// 6. 构建所有节点
			Map<String, BaseNode> allNodes = buildAllNodes(sourceData);
			log.info("构建完成，共 {} 个节点", allNodes.size());

			// 8. Redis索引管理
			// 创建新版本索引
			createRedisSearchIndex(newVersion);

			// 7. 保存到 Redis
			saveNodesToRedis(allNodes, newVersion);

			// 9. 重放增量数据到新版本
			replayIncrementalOperations(newVersion);

			// 9.5. 同步缓存的状态更新到新版本
			syncCachedStatusToNewVersion(newVersion);

			// 10. 切换版本
			switchToNewVersionSafely(newVersion);

			// 11. 清理构建状态和队列
			cleanupBuildStatus();

			// 12. 异步清理旧版本
			cleanupOldVersionAsync(currentVersion);

			// 删除旧版本索引
			dropRedisSearchIndex(currentVersion);

			log.info("版本化构建成功完成 - 从版本 {} 切换到版本 {}", currentVersion, newVersion);

		} catch (Exception e) {
			log.error("版本化构建过程中发生异常 - 当前版本: {}, 新版本: {}", currentVersion, newVersion, e);
			// 构建失败时清理状态
			cleanupBuildStatus();
			handleBuildFailure(newVersion, e);
		} finally {
			if (lockAcquired) {
				lock.unlock();
			}
		}
	}


	/**
	 * 准备版本信息
	 *
	 * @return 版本信息对象
	 */
	private VersionInfo prepareVersionInfo() {
		String currentVersion = getCurrentVersion();
		String newVersion = generateNewVersion(currentVersion);
		return new VersionInfo(currentVersion, newVersion);
	}

	/**
	 * 获取源数据
	 *
	 * @return 树构建数据对象
	 * @throws TreeBuildException 获取数据失败时抛出
	 */
	private TreeBuildData fetchSourceData() throws TreeBuildException {
		try {
			// 获取部门数据
			R<List<Dept>> deptResult = sysClient.getAllDept();
			if (!deptResult.isSuccess() || deptResult.getData() == null) {
				throw new TreeBuildException("获取部门数据失败", deptResult.getMsg());
			}
			List<Dept> deptList = deptResult.getData();

			// 获取终端数据
			List<TargetDeviceTreeDto> deviceList = treeMapper.selectDeviceNode();
			if (deviceList == null) {
				log.warn("终端数据为空，将构建仅包含部门的树结构");
				deviceList = Collections.emptyList();
			}
			// 设置acc
			fetchRealTimeLocations(deviceList);

			// 获取终端类型字典
			Map<String, String> typeNameMap = fetchDeviceTypeNames();

			return new TreeBuildData(deptList, deviceList, typeNameMap);
		} catch (TreeBuildException e) {
			throw e;
		} catch (Exception e) {
			throw new TreeBuildException("获取源数据失败", e);
		}
	}

	private void fetchRealTimeLocations(List<TargetDeviceTreeDto> deviceList) {
		Map<Long, TargetDeviceTreeDto> map = deviceList.stream().filter(item -> models.contains(item.getModel()))
			.collect(toMap(TargetDeviceTreeDto::getId, Function.identity()));
		Set<String> deviceIds = map.keySet().stream().map(Object::toString).collect(Collectors.toSet());
		HashOperations<String, String, String> opsForHash = stringRedisTemplate.opsForHash();
		List<String> values = opsForHash.multiGet(CommonConstant.REDIS_CACHE_LAST_POST, deviceIds);
		for (String value : values) {
			if (value != null) {
				Location location = JSON.parseObject(value, Location.class);
				map.get(location.getDeviceId()).setAcc(location.getStatus() & 1);
			}
		}
	}

	/**
	 * 获取终端类型名称映射
	 *
	 * @return 终端类型ID到名称的映射
	 */
	private Map<String, String> fetchDeviceTypeNames() {
		try {
			R<List<DictBiz>> dictResult = dictBizClient.getList(DictCodeConstant.DEVICE_TYPE);
			if (dictResult.isSuccess() && dictResult.getData() != null) {
				return dictResult.getData().stream()
					.collect(toMap(DictBiz::getDictKey, DictBiz::getDictValue, (v1, v2) -> v1));
			}
		} catch (Exception e) {
			log.warn("获取终端类型字典失败，将使用默认名称", e);
		}
		return new HashMap<>();
	}

	/**
	 * 构建所有节点
	 *
	 * @param sourceData 源数据
	 * @return 所有节点的映射
	 */
	private Map<String, BaseNode> buildAllNodes(TreeBuildData sourceData) {
		Map<String, BaseNode> allNodes = new HashMap<>(10000);

		// 1. 构建部门节点
		Map<String, DeptNode> deptNodeMap = buildDeptNodes(sourceData.getDeptList());
		allNodes.putAll(deptNodeMap);

		// 2. 处理终端数据，构建终端相关节点
		if (!sourceData.getDeviceList().isEmpty()) {
			processDeviceData(sourceData.getDeviceList(), sourceData.getTypeNameMap(),
				deptNodeMap, allNodes);
		}

		// 3. 聚合部门统计数据
		aggregateDeptStatistics(deptNodeMap);

		// 4. 移除空部门
		removeEmptyDeptNodes(deptNodeMap, allNodes);

		return allNodes;
	}

	/**
	 * 构建部门节点
	 *
	 * @param deptList 部门列表
	 * @return 部门节点映射
	 */
	private Map<String, DeptNode> buildDeptNodes(List<Dept> deptList) {
		return deptList.stream()
			.map(this::createDeptNode)
			.collect(Collectors.toMap(
				BaseNode::getId,
				Function.identity()
			));
	}

	/**
	 * 创建部门节点
	 *
	 * @param dept 部门实体
	 * @return 部门节点
	 */
	private DeptNode createDeptNode(Dept dept) {
		DeptNode node = new DeptNode();
		node.setId(TreeCacheKeyConstant.getPrefixedDeptId(dept.getId()));
		node.setDeptId(dept.getId());
		node.setParentId(TreeCacheKeyConstant.getPrefixedDeptId(dept.getParentId()));
		node.setName(dept.getDeptName());
		node.setType(NodeType.DEPT);
		node.setTotal(0L);
		node.setOnlineNum(0L);
		node.setSelfTotal(0L);
		node.setSelfOnlineNum(0L);
		return node;
	}

	/**
	 * 处理终端数据，构建终端相关节点
	 *
	 * @param deviceList  终端列表
	 * @param typeNameMap 终端类型名称映射
	 * @param deptNodeMap 部门节点映射
	 * @param allNodes    所有节点映射
	 */
	private void processDeviceData(List<TargetDeviceTreeDto> deviceList,
								   Map<String, String> typeNameMap,
								   Map<String, DeptNode> deptNodeMap,
								   Map<String, BaseNode> allNodes) {

		// 按"部门-终端类型-监控对象"的复合键对终端进行分组
		Map<String, List<TargetDeviceTreeDto>> deviceGroups = groupDevicesByCompositeKey(deviceList);

		// 终端类型节点映射
		Map<String, DeviceTypeNode> typeNodeMap = new HashMap<>();

		// 处理每个终端组
		for (Map.Entry<String, List<TargetDeviceTreeDto>> entry : deviceGroups.entrySet()) {
			processDeviceGroup(entry.getValue(), typeNameMap, deptNodeMap, typeNodeMap, allNodes);
		}
	}

	/**
	 * 按复合键对终端进行分组
	 *
	 * @param deviceList 终端列表
	 * @return 分组后的终端映射
	 */
	private Map<String, List<TargetDeviceTreeDto>> groupDevicesByCompositeKey(List<TargetDeviceTreeDto> deviceList) {
		return deviceList.stream()
			.filter(dto -> dto.getTargetId() != null && dto.getDeptId() != null && dto.getDeviceType() != null)
			.collect(Collectors.groupingBy(dto ->
				getTargetNodeId(dto)
			));
	}

	/**
	 * 处理单个终端组
	 *
	 * @param devicesInGroup 组内终端列表
	 * @param typeNameMap    终端类型名称映射
	 * @param deptNodeMap    部门节点映射
	 * @param typeNodeMap    终端类型节点映射
	 * @param allNodes       所有节点映射
	 */
	private void processDeviceGroup(List<TargetDeviceTreeDto> devicesInGroup,
									Map<String, String> typeNameMap,
									Map<String, DeptNode> deptNodeMap,
									Map<String, DeviceTypeNode> typeNodeMap,
									Map<String, BaseNode> allNodes) {

		if (devicesInGroup.isEmpty()) {
			return;
		}

		TargetDeviceTreeDto representativeDevice = devicesInGroup.get(0);

		// 获取部门节点
		String deptNodeId = TreeCacheKeyConstant.getPrefixedDeptId(representativeDevice.getDeptId());
		DeptNode deptNode = deptNodeMap.get(deptNodeId);
		if (deptNode == null) {
			log.warn("未找到部门节点: {}", representativeDevice.getDeptId());
			return;
		}

		// 获取或创建终端类型节点
		DeviceTypeNode typeNode = getOrCreateDeviceTypeNode(representativeDevice, deptNode,
			typeNameMap, typeNodeMap, allNodes);

		// 根据终端数量选择处理方式
		if (devicesInGroup.size() == 1) {
			processSingleDeviceGroup(representativeDevice, deptNode, typeNode, allNodes);
		} else {
			processMultipleDeviceGroup(devicesInGroup, deptNode, typeNode, allNodes);
		}
	}


	/**
	 * 判断终端是否在线
	 *
	 * @param device 终端DTO
	 * @return 是否在线
	 */
	private boolean isDeviceOnline(TargetDeviceTreeDto device) {
		return device.getOnline() != null && device.getOnline().equals(DeviceStatusConstant.ONLINE);
	}

	/**
	 * 获取在线状态增量
	 *
	 * @param device 终端DTO
	 * @return 在线状态增量（1或0）
	 */
	private long getOnlineIncrement(TargetDeviceTreeDto device) {
		return isDeviceOnline(device) ? 1L : 0L;
	}

	/**
	 * 获取或创建终端类型节点
	 *
	 * @param device      终端DTO
	 * @param deptNode    部门节点
	 * @param typeNameMap 类型名称映射
	 * @param typeNodeMap 类型节点映射
	 * @param allNodes    所有节点映射
	 * @return 终端类型节点
	 */
	private DeviceTypeNode getOrCreateDeviceTypeNode(TargetDeviceTreeDto device,
													 DeptNode deptNode,
													 Map<String, String> typeNameMap,
													 Map<String, DeviceTypeNode> typeNodeMap,
													 Map<String, BaseNode> allNodes) {

		String deviceTypeKey = TreeCacheKeyConstant.getPrefixedDeviceTypeId(
			deptNode.getDeptId(), device.getDeviceType());

		return typeNodeMap.computeIfAbsent(deviceTypeKey, key -> {
			DeviceTypeNode newNode = new DeviceTypeNode();
			newNode.setId(deviceTypeKey);
			newNode.setDeptId(deptNode.getDeptId());
			newNode.setParentId(deptNode.getId());
			newNode.setName(getDeviceTypeName(device.getDeviceType(), typeNameMap));
			newNode.setType(NodeType.DEVICE_TYPE);
			newNode.setTotal(0L);
			newNode.setOnlineNum(0L);
			allNodes.put(key, newNode);
			return newNode;
		});
	}

	/**
	 * 获取终端类型名称
	 *
	 * @param deviceType  终端类型ID
	 * @param typeNameMap 类型名称映射
	 * @return 终端类型名称
	 */
	private String getDeviceTypeName(Integer deviceType, Map<String, String> typeNameMap) {
		return typeNameMap.getOrDefault(deviceType.toString(),
			"未知分类 " + deviceType);
	}

	/**
	 * 处理单终端组（监控对象下只有一个终端）
	 *
	 * @param device   终端DTO
	 * @param deptNode 部门节点
	 * @param typeNode 终端类型节点
	 * @param allNodes 所有节点映射
	 */
	private void processSingleDeviceGroup(TargetDeviceTreeDto device,
										  DeptNode deptNode,
										  DeviceTypeNode typeNode,
										  Map<String, BaseNode> allNodes) {

		String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(device));

		// 获取或创建监控对象节点
		TargetNode targetNode = (TargetNode) allNodes.computeIfAbsent(targetNodeId, k ->
			createTargetNode(device));

		// 设置终端状态到监控对象节点（作为叶子节点）
		setDevicePropertiesToTarget(targetNode, device);

		// 统计数据聚合
		long onlineIncrement = getOnlineIncrement(device);
		updateNodeStatistics(targetNode, 1L, onlineIncrement);
		updateNodeStatistics(typeNode, 1L, onlineIncrement);
		updateDeptSelfStatistics(deptNode, 1L, onlineIncrement);
	}

	@NotNull
	private static String getTargetNodeId(TargetDeviceTreeDto device) {
		return device.getDeptId() + "_" + device.getDeviceType() + "_" + device.getTargetId();
	}

	/**
	 * 处理多终端组（监控对象下有多个终端）
	 *
	 * @param devicesInGroup 终端组
	 * @param deptNode       部门节点
	 * @param typeNode       终端类型节点
	 * @param allNodes       所有节点映射
	 */
	private void processMultipleDeviceGroup(List<TargetDeviceTreeDto> devicesInGroup,
											DeptNode deptNode,
											DeviceTypeNode typeNode,
											Map<String, BaseNode> allNodes) {

		TargetDeviceTreeDto firstDevice = devicesInGroup.get(0);

		String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(firstDevice));

		// 获取或创建监控对象节点
		TargetNode targetNode = (TargetNode) allNodes.computeIfAbsent(targetNodeId, k ->
			createTargetNode(firstDevice));

		// 处理组内每个终端，创建终端节点
		long totalDeviceCount = 0;
		long onlineDeviceCount = 0;

		for (TargetDeviceTreeDto deviceDto : devicesInGroup) {
			DeviceNode deviceNode = createDeviceNode(deviceDto);
			allNodes.put(deviceNode.getId(), deviceNode);

			totalDeviceCount++;
			onlineDeviceCount += getOnlineIncrement(deviceDto);
		}

		// 统计数据聚合
		updateNodeStatistics(targetNode, totalDeviceCount, onlineDeviceCount);
		updateNodeStatistics(typeNode, totalDeviceCount, onlineDeviceCount);
		updateDeptSelfStatistics(deptNode, totalDeviceCount, onlineDeviceCount);
	}

	/**
	 * 创建监控对象节点
	 *
	 * @param device 终端DTO
	 * @return 监控对象节点
	 */
	private TargetNode createTargetNode(TargetDeviceTreeDto device) {
		TargetNode targetNode = new TargetNode();
		targetNode.setId(TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(device)));
		targetNode.setTargetId(device.getTargetId());
		targetNode.setDeviceId(device.getId());
		targetNode.setDeptId(device.getDeptId());
		targetNode.setParentId(TreeCacheKeyConstant.getPrefixedDeviceTypeId(device.getDeptId(), device.getDeviceType()));
		targetNode.setName(device.getName());
		targetNode.setType(NodeType.TARGET);
		targetNode.setTotal(0L);
		targetNode.setOnlineNum(0L);
		return targetNode;
	}

	/**
	 * 创建终端节点
	 *
	 * @param deviceTreeDto 终端DTO
	 * @return 终端节点
	 */
	private DeviceNode createDeviceNode(TargetDeviceTreeDto deviceTreeDto) {
		DeviceNode deviceNode = new DeviceNode();
		deviceNode.setId(TreeCacheKeyConstant.getPrefixedDeviceId(deviceTreeDto.getId()));
		deviceNode.setDeviceId(deviceTreeDto.getId());
		deviceNode.setTargetId(deviceTreeDto.getTargetId());
		deviceNode.setDeptId(deviceTreeDto.getDeptId());
		deviceNode.setParentId(TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceTreeDto)));
		deviceNode.setName(deviceTreeDto.getUniqueId());
		deviceNode.setUniqueId(deviceTreeDto.getUniqueId());
		deviceNode.setType(NodeType.DEVICE);
		deviceNode.setOnline(deviceTreeDto.getOnline());
		deviceNode.setFusionState(deviceTreeDto.getFusionState());
		deviceNode.setCreateAccount(deviceTreeDto.getCreateAccount());
		deviceNode.setAcc(deviceTreeDto.getAcc());
		deviceNode.setChannelNum(deviceTreeDto.getChannelNum());
		deviceNode.setDeviceType(deviceTreeDto.getDeviceType());
		deviceNode.setCategory(deviceTreeDto.getCategory());
		return deviceNode;
	}

	/**
	 * 设置终端属性到监控对象节点
	 *
	 * @param targetNode 监控对象节点
	 * @param device     终端DTO
	 */
	private void setDevicePropertiesToTarget(TargetNode targetNode, TargetDeviceTreeDto device) {
		targetNode.setOnline(device.getOnline());
		targetNode.setFusionState(device.getFusionState());
		targetNode.setUniqueId(device.getUniqueId());
		targetNode.setDeptId(device.getDeptId());
		targetNode.setCreateAccount(device.getCreateAccount());
		targetNode.setCategory(device.getCategory());
		targetNode.setDeviceType(device.getDeviceType());
		targetNode.setAcc(device.getAcc());
		targetNode.setChannelNum(device.getChannelNum());
	}

	/**
	 * 更新节点统计数据
	 *
	 * @param node            节点（支持DeviceTypeNode和TargetNode）
	 * @param totalIncrement  总数增量
	 * @param onlineIncrement 在线数增量
	 */
	private void updateNodeStatistics(BaseNode node, long totalIncrement, long onlineIncrement) {
		if (node instanceof DeviceTypeNode) {
			DeviceTypeNode typeNode = (DeviceTypeNode) node;
			typeNode.setTotal(typeNode.getTotal() + totalIncrement);
			typeNode.setOnlineNum(typeNode.getOnlineNum() + onlineIncrement);
		} else if (node instanceof TargetNode) {
			TargetNode targetNode = (TargetNode) node;
			targetNode.setTotal(targetNode.getTotal() + totalIncrement);
			targetNode.setOnlineNum(targetNode.getOnlineNum() + onlineIncrement);
		}
	}

	/**
	 * 更新部门自身统计数据
	 *
	 * @param deptNode        部门节点
	 * @param totalIncrement  总数增量
	 * @param onlineIncrement 在线数增量
	 */
	private void updateDeptSelfStatistics(DeptNode deptNode, long totalIncrement, long onlineIncrement) {
		deptNode.setSelfTotal(deptNode.getSelfTotal() + totalIncrement);
		deptNode.setSelfOnlineNum(deptNode.getSelfOnlineNum() + onlineIncrement);
	}

	/**
	 * 聚合部门统计数据（优化后的递归算法）
	 *
	 * @param deptNodeMap 部门节点映射
	 */
	private void aggregateDeptStatistics(Map<String, DeptNode> deptNodeMap) {
		// 构建父子关系映射
		Map<String, List<DeptNode>> childrenMap = buildDeptChildrenMap(deptNodeMap);
		Set<String> visited = new HashSet<>();

		// 从根节点开始递归聚合
		deptNodeMap.values().stream()
			.filter(node -> TreeCacheKeyConstant.ROOT_DEPT_PARENT_ID.equals(node.getParentId()))
			.forEach(rootNode -> aggregateRecursively(rootNode, childrenMap, visited));
	}

	/**
	 * 构建部门父子关系映射
	 *
	 * @param deptNodeMap 部门节点映射
	 * @return 父ID到子节点列表的映射
	 */
	private Map<String, List<DeptNode>> buildDeptChildrenMap(Map<String, DeptNode> deptNodeMap) {
		return deptNodeMap.values().stream()
			.collect(Collectors.groupingBy(DeptNode::getParentId));
	}

	/**
	 * 递归聚合部门统计数据
	 *
	 * @param node        当前节点
	 * @param childrenMap 父子关系映射
	 * @param visited     已访问节点集合
	 * @return 统计数据数组 [总数, 在线数]
	 */
	private long[] aggregateRecursively(DeptNode node,
										Map<String, List<DeptNode>> childrenMap,
										Set<String> visited) {

		if (visited.contains(node.getId())) {
			return new long[]{node.getTotal(), node.getOnlineNum()};
		}
		visited.add(node.getId());

		// 初始化为自身终端数
		long totalDevices = node.getSelfTotal();
		long onlineDevices = node.getSelfOnlineNum();

		// 递归聚合子部门
		List<DeptNode> children = childrenMap.get(node.getId());
		if (children != null) {
			for (DeptNode child : children) {
				long[] childStats = aggregateRecursively(child, childrenMap, visited);
				totalDevices += childStats[0];
				onlineDevices += childStats[1];
			}
		}

		node.setTotal(totalDevices);
		node.setOnlineNum(onlineDevices);
		return new long[]{totalDevices, onlineDevices};
	}

	/**
	 * 移除空部门节点
	 *
	 * @param deptNodeMap 部门节点映射
	 * @param allNodes    所有节点映射
	 */
	private void removeEmptyDeptNodes(Map<String, DeptNode> deptNodeMap, Map<String, BaseNode> allNodes) {
		Set<String> keysToRemove = deptNodeMap.entrySet().stream()
			.filter(entry -> entry.getValue().getTotal() == 0L)
			.map(Map.Entry::getKey)
			.collect(Collectors.toSet());

		if (!keysToRemove.isEmpty()) {
			log.info("正在移除 {} 个终端总数为 0 的部门...", keysToRemove.size());
			keysToRemove.forEach(key -> {
				deptNodeMap.remove(key);
				allNodes.remove(key);
			});
		}
	}

	/**
	 * 保存节点到Redis
	 *
	 * @param allNodes 所有节点映射
	 * @param version  版本号
	 */
	private void saveNodesToRedis(Map<String, BaseNode> allNodes, String version) {
		log.info("开始通过分批Pipeline将 {} 个节点写入Redis新版本: {}...", allNodes.size(), version);
		long startTime = System.currentTimeMillis();

		final int BATCH_SIZE = TreeCacheKeyConstant.PIPELINE_BATCH_SIZE;
		List<Map.Entry<String, BaseNode>> entryList = new ArrayList<>(allNodes.entrySet());
		int totalSize = entryList.size();

		for (int i = 0; i < totalSize; i += BATCH_SIZE) {
			int end = Math.min(i + BATCH_SIZE, totalSize);
			List<Map.Entry<String, BaseNode>> batch = entryList.subList(i, end);

			log.info("正在处理批次: {}/{} ({}->{})",
				(i / BATCH_SIZE) + 1, (totalSize + BATCH_SIZE - 1) / BATCH_SIZE, i, end - 1);

			processBatch(batch, version);
		}

		long endTime = System.currentTimeMillis();
		log.info("分批Pipeline写入完成，总耗时: {} ms", (endTime - startTime));
	}

	/**
	 * 处理单个批次的节点写入
	 *
	 * @param batch   批次节点列表
	 * @param version 版本号
	 */
	private void processBatch(List<Map.Entry<String, BaseNode>> batch, String version) {
		treeRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
			batch.forEach(entry -> {
				String redisKey = entry.getKey();
				BaseNode node = entry.getValue();
				if (node == null || node.getId() == null) {
					return;
				}

				try {
					saveNodeToRedis(connection, redisKey, node, version);
				} catch (Exception e) {
					log.error("在Pipeline中处理节点 {} 时发生错误", node.getId(), e);
				}
			});
			return null;
		});
	}

	/**
	 * 保存单个节点到Redis
	 *
	 * @param connection Redis连接
	 * @param redisKey   Redis键
	 * @param node       节点对象
	 * @param version    版本号
	 */
	@SuppressWarnings("unchecked")
	private void saveNodeToRedis(Object connection, String redisKey, BaseNode node, String version) {
		org.springframework.data.redis.connection.RedisConnection redisConn =
			(org.springframework.data.redis.connection.RedisConnection) connection;

		try {
			// 使用版本化的节点key
			String nodeKey = TreeCacheKeyConstant.getVersionedNodeKey(version, redisKey);
			Map<byte[], byte[]> byteMap = convertNodeToMap(node);

			redisConn.hMSet(nodeKey.getBytes(), byteMap);

			// 处理父子关系
			if (node.getParentId() != null) {
				String childrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(version, node.getParentId());
				double score = getScore(node);
				redisConn.zAdd(childrenKey.getBytes(), score, redisKey.getBytes());
			}

			// 处理终端节点的特殊索引
			if (node instanceof DeviceNode) {
				DeviceNode dev = (DeviceNode) node;
				if (dev.getCreateAccount() != null) {
					String creatorKey = TreeCacheKeyConstant.getVersionedUserDevicesKey(version, dev.getCreateAccount());
					redisConn.sAdd(creatorKey.getBytes(), redisKey.getBytes());
				}
				if (dev.getDeptId() != null) {
					String deptKey = TreeCacheKeyConstant.getVersionedDeptDevicesKey(version, dev.getDeptId().toString());
					redisConn.sAdd(deptKey.getBytes(), redisKey.getBytes());
				}
			}
		} catch (Exception e) {
			log.error("保存节点到Redis失败: {}", node.getId(), e);
			throw new RuntimeException("保存节点失败", e);
		}
	}

	@NotNull
	private Map<byte[], byte[]> convertNodeToMap(BaseNode node) {
		Map<String, Object> nodeMap = objectMapper.convertValue(node, Map.class);

		Map<byte[], byte[]> byteMap = new HashMap<>();
		nodeMap.forEach((k, v) -> {
			if (v != null) {
				byteMap.put(k.getBytes(), v.toString().getBytes());
			}
		});
		return byteMap;
	}

	/**
	 * 安全地切换到新版本
	 *
	 * @param newVersion 新版本号
	 * @throws TreeBuildException 切换失败时抛出
	 */
	private void switchToNewVersionSafely(String newVersion) throws TreeBuildException {
		boolean switchSuccess = switchToNewVersion(newVersion);
		if (!switchSuccess) {
			throw new TreeBuildException("版本切换失败，新版本数据已构建但未激活: " + newVersion);
		}
	}


	/**
	 * 删除指定版本的Redis搜索索引
	 *
	 * @param version 版本号
	 */
	private void dropRedisSearchIndex(String version) {
		if (version == null || version.isEmpty()) {
			log.warn("版本号为空，跳过删除索引操作");
			return;
		}

		String indexName = "tree_node_" + version + "_idx";
		log.info("删除Redis搜索索引: {}", indexName);

		try {
			treeRedisTemplate.execute((RedisCallback<Object>) connection -> {
				try {
					// 执行 FT.DROPINDEX 命令
					connection.execute("FT.DROPINDEX", indexName.getBytes());
					log.info("成功删除Redis搜索索引: {}", indexName);
				} catch (Exception e) {
					// 索引不存在时会抛出异常，这是正常情况
					log.info("Redis搜索索引不存在或删除失败: {} - {}", indexName, e.getMessage());
				}
				return null;
			});
		} catch (Exception e) {
			log.error("删除Redis搜索索引失败: {}", indexName, e);
		}
	}

	/**
	 * 创建指定版本的Redis搜索索引
	 *
	 * @param version 版本号
	 */
	private void createRedisSearchIndex(String version) {
		if (version == null || version.isEmpty()) {
			log.warn("版本号为空，跳过创建索引操作");
			return;
		}

		String indexName = "tree_node_" + version + "_idx";
		log.info("创建Redis搜索索引: {}", indexName);

		try {
			treeRedisTemplate.execute((RedisCallback<Object>) connection -> {
				try {
					// 构建 FT.CREATE 命令参数
					String[] createArgs = {
						"FT.CREATE",
						indexName,
						"ON", "HASH",
						"PREFIX", "3",
						"tree:" + version + ":node:target:",
						"tree:" + version + ":node:dept:",
						"tree:" + version + ":node:device:",
						"LANGUAGE", "chinese",
						"SCHEMA",
						"name", "TEXT",
						"uniqueId", "TAG", "SORTABLE",
						"type", "TAG",
						"parentId", "TAG",
						"deviceId", "TAG",
						"targetId", "TAG",
						"createAccount", "TAG",
						"deptId", "TAG"
					};

					// 将字符串参数转换为字节数组
					byte[][] args = new byte[createArgs.length][];
					for (int i = 0; i < createArgs.length; i++) {
						args[i] = createArgs[i].getBytes();
					}

					// 执行命令
					connection.execute(createArgs[0], java.util.Arrays.copyOfRange(args, 1, args.length));
					log.info("成功创建Redis搜索索引: {}", indexName);
				} catch (Exception e) {
					log.error("创建Redis搜索索引失败: {} - {}", indexName, e.getMessage());
					throw new RuntimeException("创建Redis搜索索引失败: " + indexName, e);
				}
				return null;
			});
		} catch (Exception e) {
			log.error("创建Redis搜索索引失败: {}", indexName, e);
			throw new RuntimeException("创建Redis搜索索引失败: " + indexName, e);
		}
	}

	/**
	 * 处理构建失败
	 *
	 * @param newVersion 新版本号
	 * @param e          异常
	 */
	private void handleBuildFailure(String newVersion, Exception e) {
		// 构建失败时，新版本数据会因为TTL自动过期，不影响当前版本
		// 可以考虑主动清理新版本数据
		if (newVersion != null) {
			log.warn("构建失败，新版本 {} 的数据将通过TTL自动过期", newVersion);
		}
		throw new TreeBuildException("树形结构构建失败", e);
	}

	/**
	 * 计算节点在Redis ZSet中的排序score
	 * 对于TARGET和DEVICE节点，使用名称的哈希值来实现字符串排序
	 *
	 * @param node 节点对象
	 * @return 排序score值
	 */
	private double getScore(BaseNode node) {
		switch (node.getType()) {
			case NodeType.DEPT:
				// 部门节点：使用部门ID的数值
				try {
					return node.getDeptId();
				} catch (NumberFormatException e) {
					log.warn("部门节点ID无法转换为数值: {}, 使用默认值", node.getDeptId());
					return TreeCacheKeyConstant.DEFAULT_SORT_ORDER;
				}
			case NodeType.DEVICE_TYPE:
				// 终端类型节点：使用终端类型ID的数值部分
				try {
					String[] parts = node.getId().split("_");
					if (parts.length >= 3) {
						return Double.parseDouble(parts[2]);
					}
					return TreeCacheKeyConstant.DEFAULT_SORT_ORDER;
				} catch (NumberFormatException e) {
					log.warn("终端类型节点ID无法解析: {}, 使用默认值", node.getId());
					return TreeCacheKeyConstant.DEFAULT_SORT_ORDER;
				}
			case NodeType.TARGET:
			case NodeType.DEVICE:
				// 监控对象和终端节点：使用名称的哈希值实现字符串排序
				return getNameBasedScore(node.getName());
			default:
				return TreeCacheKeyConstant.DEFAULT_SORT_ORDER;
		}
	}

	/**
	 * 基于名称计算排序score
	 * 使用字符串前几个字符的Unicode值来实现字典序排序
	 *
	 * @param name 节点名称
	 * @return 基于名称的score值
	 */
	private double getNameBasedScore(String name) {
		if (name == null || name.trim().isEmpty()) {
			return TreeCacheKeyConstant.DEFAULT_SORT_ORDER;
		}

		String trimmedName = name.trim().toLowerCase(); // 转小写确保排序一致性
		double score = 0.0;

		// 使用前3个字符计算score，避免数值过大
		int maxChars = Math.min(3, trimmedName.length());
		for (int i = 0; i < maxChars; i++) {
			char c = trimmedName.charAt(i);

			// 使用256进制来组合字符值，确保字典序
			// 第一个字符权重最大：256^2，第二个字符：256^1，第三个字符：256^0
			score += (int) c * Math.pow(256, maxChars - 1 - i);
		}

		// 为了避免score过大导致精度问题，我们对结果进行缩放
		// 将结果限制在0-999999范围内
		score = score % 1000000;

		log.debug("节点名称: {}, 计算得到的score: {}", name, score);
		return score;
	}


	@SneakyThrows
	@Override
	public List<BaseNodeVO> getTreeByCreator(String userId) {
		if (userId == null || userId.isEmpty()) {
			return Collections.emptyList();
		}
		// 显式指定结果序列化器为String
		final RedisSerializer<String> serializer = RedisSerializer.string();
		String jsonResult = treeRedisTemplate.execute(getTreeByCreatorScript, serializer, serializer, Collections.singletonList(userId));

		if (jsonResult.isEmpty() || "[]".equals(jsonResult)) {
			return Collections.emptyList();
		}
		// Lua脚本返回的是一棵树
		List<BaseNodeVO> treeNodes = objectMapper.readValue(jsonResult, new com.fasterxml.jackson.core.type.TypeReference<List<BaseNodeVO>>() {
		});

		// 对树形结构进行节点合并和统计重新计算处理
		processTreeNodes(treeNodes);

		return treeNodes;
	}

	/**
	 * 对树形结构进行节点合并和统计重新计算处理
	 *
	 * @param treeNodes 树节点列表
	 */
	private void processTreeNodes(List<BaseNodeVO> treeNodes) {
		if (treeNodes == null || treeNodes.isEmpty()) {
			return;
		}

		// 1. 遍历所有节点进行节点合并处理
		for (BaseNodeVO node : treeNodes) {
			mergeTargetDeviceNodes(node);
		}

		// 2. 重新计算统计数据
		for (BaseNodeVO node : treeNodes) {
			recalculateStatistics(node);
		}
	}

	/**
	 * 递归合并 TargetNode 下只有一个 DeviceNode 的情况
	 *
	 * @param node 当前节点
	 */
	private void mergeTargetDeviceNodes(BaseNodeVO node) {
		if (node == null || node.getChildren() == null) {
			return;
		}

		// 先递归处理子节点
		for (BaseNodeVO child : node.getChildren()) {
			mergeTargetDeviceNodes(child);
		}

		// 如果当前节点是 TargetNode，检查是否需要合并
		if (node instanceof TargetNodeVO) {
			TargetNodeVO targetNode = (TargetNodeVO) node;
			List<BaseNodeVO> children = targetNode.getChildren();

			// 如果只有一个子节点且是 DeviceNode，则进行合并
			if (children != null && children.size() == 1 && children.get(0) instanceof DeviceNodeVO) {
				DeviceNodeVO deviceNode = (DeviceNodeVO) children.get(0);

				// 将 DeviceNodeVO 的数据合并到 TargetNodeVO 中
				mergeDeviceDataToTarget(targetNode, deviceNode);

				// 将 DeviceNodeVO 的子节点转移到 TargetNodeVO
				if (deviceNode.getChildren() != null) {
					targetNode.setChildren(deviceNode.getChildren());
				} else {
					targetNode.setChildren(null);
				}
			}
		}
	}

	/**
	 * 将 DeviceNode 的数据合并到 TargetNode 中
	 *
	 * @param targetNode 目标节点
	 * @param deviceNode 设备节点
	 */
	private void mergeDeviceDataToTarget(TargetNodeVO targetNode, DeviceNodeVO deviceNode) {
		// 合并 DeviceNode 的字段到 TargetNode
		targetNode.setDeviceId(deviceNode.getDeviceId());
		targetNode.setCreateAccount(deviceNode.getCreateAccount());
		targetNode.setCategory(deviceNode.getCategory());
		targetNode.setDeviceType(deviceNode.getDeviceType());
		targetNode.setChannelNum(deviceNode.getChannelNum());
		targetNode.setOnline(deviceNode.getOnline());
		targetNode.setFusionState(deviceNode.getFusionState());
		targetNode.setUniqueId(deviceNode.getUniqueId());
		targetNode.setAcc(deviceNode.getAcc());
	}

	/**
	 * 递归重新计算节点的统计数据
	 *
	 * @param node 当前节点
	 * @return 返回统计结果 [total, onlineNum]
	 */
	private long[] recalculateStatistics(BaseNodeVO node) {
		if (node == null) {
			return new long[]{0, 0};
		}

		long total = 0;
		long onlineNum = 0;

		// 如果有子节点，递归计算子节点的统计数据
		if (node.getChildren() != null && !node.getChildren().isEmpty()) {
			for (BaseNodeVO child : node.getChildren()) {
				long[] childStats = recalculateStatistics(child);
				total += childStats[0];
				onlineNum += childStats[1];
			}
		} else {
			// 叶子节点的统计逻辑
			if (node.getType().equals(NodeType.DEVICE)) {
				DeviceNodeVO deviceNode = (DeviceNodeVO) node;
				total = 1; // 设备节点计为1个终端
				onlineNum = (deviceNode.getOnline() != null && deviceNode.getOnline().equals(DeviceStatusConstant.ONLINE)) ? 1 : 0;
			} else if (node.getType().equals(NodeType.TARGET)) {
				TargetNodeVO targetNode = (TargetNodeVO) node;
				// 如果 TargetNodeVO 已经合并了 DeviceNodeVO 的数据，则按设备节点计算
				if (targetNode.getDeviceId() != null) {
					total = 1;
					onlineNum = (targetNode.getOnline() != null && targetNode.getOnline().equals(DeviceStatusConstant.ONLINE)) ? 1 : 0;
				}
			}
		}

		// 更新当前节点的统计数据
		if (node instanceof TargetNodeVO) {
			TargetNodeVO targetNode = (TargetNodeVO) node;
			targetNode.setTotal(total);
			targetNode.setOnlineNum(onlineNum);
		} else if (node instanceof DeptNodeVO) {
			DeptNodeVO deptNode = (DeptNodeVO) node;
			deptNode.setTotal(total);
			deptNode.setOnlineNum(onlineNum);
		} else if (node instanceof DeviceTypeNodeVO) {
			DeviceTypeNodeVO deviceTypeNode = (DeviceTypeNodeVO) node;
			deviceTypeNode.setTotal(total);
			deviceTypeNode.setOnlineNum(onlineNum);
		}

		return new long[]{total, onlineNum};
	}


	/**
	 * 根据模式通过Lua脚本清除Redis中的key
	 *
	 * @param pattern Redis key模式
	 * @return 删除的key数量
	 */
	private long clearKeysByPattern(String pattern) {
		try {
			// ARGV[1] scan_count, ARGV[2] batch_size
			Long deletedCount = treeRedisTemplate.execute(clearKeysByPatternScript,
				Collections.singletonList(pattern), TreeCacheKeyConstant.LUA_SCAN_COUNT, TreeCacheKeyConstant.LUA_SCAN_COUNT);
			return deletedCount != null ? deletedCount : 0L;
		} catch (Exception e) {
			log.error("使用Lua脚本清除模式 {} 的key失败", pattern, e);
			throw new RuntimeException("清除模式 " + pattern + " 失败", e);
		}
	}


	@SneakyThrows
	@Override
	public Page<TreeSearchData> searchNodes(String keyword, long current, long pageSize) {
		Page<TreeSearchData> page = new Page<>(current, pageSize);
		if (keyword == null || keyword.trim().isEmpty()) {
			return page.setRecords(Collections.emptyList()).setTotal(0);
		}
		// 1. 从Client获取RediSearch实例，使用当前版本的索引
		String currentVersion = getCurrentVersion();
		String indexName = "tree_node_" + currentVersion + "_idx";
		RediSearch rediSearch = rediSearchClient.getRediSearch(indexName);

		// 2. 构建查询语句
		String query = String.format("(@name:%s*) | (@uniqueId:{%s})", keyword, keyword);

		List<String> deptIds;
		boolean isAdmin = ceTokenUtil.isAdmin();
		if (!isAdmin) {
			DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();

			boolean isPerson = ceDataAuth.getGnDataAuthType().equals(DataAuthCE.CE_DATA_AUTH_TYPE_SELF);
			if (isPerson) {
				query = String.format("(%s) @createAccount:{%s}", query, ceDataAuth.getAccount());
			} else {
				deptIds = ceDataAuth.getOrgList();
				if (deptIds != null && !deptIds.isEmpty()) {
					String deptIdFilter = String.join("|", deptIds);
					query = String.format("(%s) @deptId:{%s}", query, deptIdFilter);
				}
			}
		}

		long offset = (current - 1) * pageSize;
		// 3. 准备分页和搜索选项
		SearchOptions options = new SearchOptions()
			.withScores()
			.page(new io.github.dengliming.redismodule.redisearch.search.Page((int) offset, (int) pageSize));

		// 4. 在RediSearch实例上执行搜索
		SearchResult searchResult = rediSearch.search(query, options);

		// 5. 将结果设置回MybatisPlus Page对象
		if (searchResult == null || searchResult.getTotal() == 0) {
			return page.setRecords(Collections.emptyList()).setTotal(0);
		}

		page.setTotal(searchResult.getTotal());

		List<TreeSearchData> nodes = new ArrayList<>();
		for (Document doc : searchResult.getDocuments()) {
			Map<String, Object> fields = doc.getFields();
			try {
				TreeSearchData node = objectMapper.convertValue(fields, TreeSearchData.class);
				nodes.add(node);
			} catch (IllegalArgumentException e) {
				log.error("无法将搜索结果 {} 转换为BaseNode: {}", fields, e.getMessage());
			}
		}
		page.setRecords(nodes);
		return page;
	}

	@SneakyThrows
	private TreeResponse getDeptTreeWithExpandedNodes(Set<String> expandedNodeIds) {
		// 构建KEYS参数：第一个是数量，后面是具体的nodeId列表
		List<String> keys = new ArrayList<>();
		keys.add(String.valueOf(expandedNodeIds.size()));
		keys.addAll(expandedNodeIds);

		String jsonResult = treeRedisTemplate.execute(getDeptTreeWithExpandedNodesScript, keys);

		log.debug("getDeptTreeWithExpandedNodes - expandedNodeIds: {}, jsonResult: {}", expandedNodeIds, jsonResult);

		if (jsonResult.isEmpty() || "[]".equals(jsonResult)) {
			log.warn("getDeptTreeWithExpandedNodes - Lua脚本返回空结果或未找到数据");
			return null;
		}

		try {
			return objectMapper.readValue(jsonResult, TreeResponse.class);
		} catch (Exception e) {
			log.error("解析部门树JSON结果失败: {}", jsonResult, e);
			return null;
		}
	}

	@Override
	public TreeResponse getDynamicTreeForWebsocket(String userCode) {
		Set<String> expandedNodeIds = TreeWebsocketListener.USER_CODE_EXPANDED_NODE_ID_SET_MAP.get(userCode);

		TreeResponse treeResponse = new TreeResponse();
		List<BaseNodeVO> nodeList;

		boolean admin = false;
		boolean isDept = false;
		String permissionType = TreeWebsocketListener.USER_CODE_PERMISSION_TYPE_MAP.get(userCode);
		if (PermissionType.ADMIN.equals(permissionType)) {
			admin = true;
		} else if (PermissionType.DEPT.equals(permissionType)) {
			isDept = true;
		}

		if (admin) {
			// 管理员权限
			treeResponse = getDeptTreeWithExpandedNodes(expandedNodeIds);
			expandedNodeIds = treeResponse.getExpandedNodeIds();
			nodeList = treeResponse.getTree();

		} else {
			if (isDept) {
				// 部门权限
				treeResponse = getDeptTreeWithExpandedNodes(expandedNodeIds);
				expandedNodeIds = treeResponse.getExpandedNodeIds();

				Set<String> userDeptIds = TreeWebsocketListener.USER_CODE_DEPT_SET_MAP.get(userCode);

				nodeList = filterAndPruneDeptTree(treeResponse.getTree(), userDeptIds);
				recalculateTreeStats(nodeList);

			} else {
				BladeUser bladeUser = TreeWebsocketListener.USER_CODE_BLADE_USER_MAP.get(userCode);
				// 个人权限
				nodeList = getTreeByCreator(bladeUser.getAccount());
			}
		}
		// 过滤非展开路径上的节点
		Set<String> nodesToKeep = buildExpandedPathNodes(nodeList, expandedNodeIds);
		List<BaseNodeVO> filteredTree = filterTreeByExpandedPath(nodeList, nodesToKeep, expandedNodeIds);
		treeResponse.setTree(filteredTree);

		return treeResponse;
	}

	@Override
	public TreeResponse getDynamicTree(Set<String> expandedNodeIds) {

		TreeResponse treeResponse = new TreeResponse();
		long startTime = System.currentTimeMillis();

		List<BaseNodeVO> nodeList;

		boolean admin = ceTokenUtil.isAdmin();
		if (admin) {
			// 管理员权限
			treeResponse = getDeptTreeWithExpandedNodes(expandedNodeIds);
			expandedNodeIds = treeResponse.getExpandedNodeIds();
			nodeList = treeResponse.getTree();
		} else {
			DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
			boolean isPerson = ceDataAuth.getGnDataAuthType().equals(DataAuthCE.CE_DATA_AUTH_TYPE_SELF);
			if (isPerson) {
				// 个人权限
				nodeList = getTreeByCreator(ceDataAuth.getAccount());
			} else {
				// 部门权限
				treeResponse = getDeptTreeWithExpandedNodes(expandedNodeIds);
				expandedNodeIds = treeResponse.getExpandedNodeIds();

				Set<String> userDeptIds = new HashSet<>(ceDataAuth.getOrgList());
				nodeList = filterAndPruneDeptTree(treeResponse.getTree(), userDeptIds);

				recalculateTreeStats(nodeList);
			}
		}


		// 过滤非展开路径上的节点
		Set<String> nodesToKeep = buildExpandedPathNodes(nodeList, expandedNodeIds);
		List<BaseNodeVO> filteredTree = filterTreeByExpandedPath(nodeList, nodesToKeep, expandedNodeIds);
		treeResponse.setTree(filteredTree);

		long endTime = System.currentTimeMillis();
		log.info("动态树构建完成，总耗时: {}ms", (endTime - startTime));
		return treeResponse;

	}


	/**
	 * [重构] 权限过滤并修剪树，只保留有权限的部门及其父节点
	 *
	 * @param nodeList     当前层级的节点列表
	 * @param permittedIds 用户有权限的部门ID集合
	 * @return 过滤和修剪后的节点列表
	 */
	private List<BaseNodeVO> filterAndPruneDeptTree(List<BaseNodeVO> nodeList, Set<String> permittedIds) {
		if (nodeList == null || nodeList.isEmpty()) {
			return Collections.emptyList();
		}

		List<BaseNodeVO> result = new ArrayList<>();
		for (BaseNodeVO node : nodeList) {
			// 递归处理子节点
			List<BaseNodeVO> children = node.getChildren();
			List<BaseNodeVO> permittedChildren = filterAndPruneDeptTree(children, permittedIds);

			// 判断当前节点是否应该保留
			// 条件：1. 节点本身在权限列表内； 或 2. 它有通过权限检查的子节点
			if (permittedIds.contains(node.getId()) || !permittedChildren.isEmpty()) {
				node.setChildren(permittedChildren); // 更新为过滤后的子节点列表
				result.add(node);
			}
		}
		return result;
	}

	/**
	 * [重构] 递归地自底向上重新计算树的统计数据
	 *
	 * @param nodeList 当前层级的节点列表
	 */
	private void recalculateTreeStats(List<BaseNodeVO> nodeList) {
		if (nodeList == null) return;
		for (BaseNodeVO node : nodeList) {
			if (node instanceof DeptNodeVO) {
				DeptNodeVO dept = (DeptNodeVO) node;
				// 先递归处理子节点
				recalculateTreeStats(dept.getChildren());

				// 重置当前节点的统计数据为自身的终端数
				dept.setTotal(dept.getSelfTotal() != null ? dept.getSelfTotal() : 0L);
				dept.setOnlineNum(dept.getSelfOnlineNum() != null ? dept.getSelfOnlineNum() : 0L);

				// 累加所有子节点的统计数据
				if (dept.getChildren() != null) {
					for (BaseNodeVO child : dept.getChildren()) {
						if (child instanceof DeptNodeVO) {
							DeptNodeVO childDept = (DeptNodeVO) child;
							dept.setTotal(dept.getTotal() + childDept.getTotal());
							dept.setOnlineNum(dept.getOnlineNum() + childDept.getOnlineNum());
						}
					}
				}
			}
		}
	}


	/**
	 * 构建展开路径上需要保留的节点集合
	 *
	 * @param tree            树结构
	 * @param expandedNodeIds 展开的节点ID集合
	 * @return 需要保留的节点ID集合
	 */
	private Set<String> buildExpandedPathNodes(List<BaseNodeVO> tree, Set<String> expandedNodeIds) {
		Set<String> nodesToKeep = new HashSet<>();
		Map<String, BaseNodeVO> nodeMap = new HashMap<>();
		Map<String, String> parentMap = new HashMap<>();

		// 构建节点映射和父子关系映射
		buildNodeMaps(tree, nodeMap, parentMap);

		// 1. 添加所有展开节点
		nodesToKeep.addAll(expandedNodeIds);

		// 2. 添加展开路径上的所有父节点
		for (String expandedNodeId : expandedNodeIds) {
			addParentPath(expandedNodeId, parentMap, nodesToKeep);
		}

		// 3. 添加展开节点的直接子节点和兄弟节点
		for (String expandedNodeId : expandedNodeIds) {
			BaseNodeVO expandedNode = nodeMap.get(expandedNodeId);
			if (expandedNode != null) {
				// 添加直接子节点
				if (expandedNode.getChildren() != null) {
					for (BaseNodeVO child : expandedNode.getChildren()) {
						nodesToKeep.add(child.getId());
					}
				}

				// 添加兄弟节点
				String parentId = parentMap.get(expandedNodeId);
				if (parentId != null) {
					BaseNodeVO parent = nodeMap.get(parentId);
					if (parent != null && parent.getChildren() != null) {
						for (BaseNodeVO sibling : parent.getChildren()) {
							nodesToKeep.add(sibling.getId());
						}
					}
				}
			}
		}

		// 4. 确保根节点被保留
		for (BaseNodeVO rootNode : tree) {
			nodesToKeep.add(rootNode.getId());
		}

		return nodesToKeep;
	}

	/**
	 * 递归构建节点映射和父子关系映射
	 */
	private void buildNodeMaps(List<BaseNodeVO> nodes, Map<String, BaseNodeVO> nodeMap, Map<String, String> parentMap) {
		if (nodes == null) return;

		for (BaseNodeVO node : nodes) {
			nodeMap.put(node.getId(), node);
			if (node.getChildren() != null) {
				for (BaseNodeVO child : node.getChildren()) {
					parentMap.put(child.getId(), node.getId());
				}
				buildNodeMaps(node.getChildren(), nodeMap, parentMap);
			}
		}
	}

	/**
	 * 添加从指定节点到根节点的完整路径
	 */
	private void addParentPath(String nodeId, Map<String, String> parentMap, Set<String> nodesToKeep) {
		String currentId = nodeId;
		while (currentId != null) {
			nodesToKeep.add(currentId);
			currentId = parentMap.get(currentId);
		}
	}

	/**
	 * 根据展开路径过滤树结构
	 *
	 * @param nodes           原始节点列表
	 * @param nodesToKeep     需要保留的节点ID集合
	 * @param expandedNodeIds 展开的节点ID集合
	 * @return 过滤后的节点列表
	 */
	private List<BaseNodeVO> filterTreeByExpandedPath(List<BaseNodeVO> nodes, Set<String> nodesToKeep, Set<String> expandedNodeIds) {
		if (nodes == null || nodes.isEmpty()) {
			return Collections.emptyList();
		}

		List<BaseNodeVO> filteredNodes = new ArrayList<>();

		for (BaseNodeVO node : nodes) {
			String type = node.getType();
			List<BaseNodeVO> children = node.getChildren();
			boolean notLeaf = type.equals(NodeType.DEPT) | type.equals(NodeType.DEVICE_TYPE) | (children != null && !children.isEmpty());
			if (!notLeaf) {
				expandedNodeIds.remove(node.getId());
			}
			// 只保留在nodesToKeep集合中的节点
			if (nodesToKeep.contains(node.getId())) {
				// 创建节点副本以避免修改原始数据
				BaseNodeVO filteredNode = copyNode(node);

				if (expandedNodeIds.contains(node.getId())) {
					// 如果是展开节点，递归处理其子节点
					List<BaseNodeVO> filteredChildren = filterTreeByExpandedPath(node.getChildren(), nodesToKeep, expandedNodeIds);
					filteredNode.setChildren(filteredChildren);
					filteredNode.setExpanded(true);
				} else {
					// 如果不是展开节点，只保留直接子节点但不展开
					List<BaseNodeVO> directChildren = new ArrayList<>();
					if (node.getChildren() != null) {
						for (BaseNodeVO child : node.getChildren()) {
							if (nodesToKeep.contains(child.getId())) {
								BaseNodeVO childCopy = copyNode(child);
								childCopy.setChildren(Collections.emptyList()); // 非展开节点的子节点不显示其子节点
								childCopy.setExpanded(false);
								directChildren.add(childCopy);
							}
						}
					}
					filteredNode.setChildren(directChildren);
					filteredNode.setExpanded(false);
				}

				// 设置hasChildren标志
				// filteredNode.setHasChildren(filteredNode.getChildren() != null && !filteredNode.getChildren().isEmpty());

				filteredNodes.add(filteredNode);
			}
		}

		return filteredNodes;
	}

	/**
	 * 复制节点（浅拷贝，不包括children）
	 */
	private BaseNodeVO copyNode(BaseNodeVO original) {
		try {
			// 使用Jackson进行深拷贝
			String json = objectMapper.writeValueAsString(original);
			BaseNodeVO copy = objectMapper.readValue(json, BaseNodeVO.class);
			copy.setChildren(null); // 清空children，后续单独处理
			return copy;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 更新节点在线状态的通用方法（使用Lua脚本）
	 */
	private boolean updateNodeOnlineStatus(DeviceNode deviceNode, Integer onlineStatus) {
		// 使用Lua脚本原子性更新节点状态和统计数据
		String currentVersion = getCurrentVersion();

		String versionedNodeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceNode.getId());

		Long result = treeRedisTemplate.execute(
			updateDeviceStatusScript,
			Collections.singletonList(versionedNodeKey),
			onlineStatus.toString(),
			currentVersion
		);
		return result > 0;

	}

	/**
	 * 更新监控对象节点字段
	 */
	private boolean updateField(DeviceNode deviceNode, String fieldName, String fieldValue) {
		// 使用Lua脚本原子性更新节点状态和统计数据
		String currentVersion = getCurrentVersion();
		String nodeId;
		if (NodeType.TARGET.equals(deviceNode.getType())) {
			nodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceNode));
		} else {
			nodeId = TreeCacheKeyConstant.getPrefixedDeviceId(deviceNode.getDeviceId());
		}
		String versionedNodeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, nodeId);

		treeRedisTemplate.opsForHash().put(versionedNodeKey, fieldName, fieldValue);
		return true;

	}


	@Override
	public void handleUpdate(DeviceInfo deviceInfo) {
		// 检查是否正在构建树
		if (isBuildingTree()) {
			IncrementalOperation operation = IncrementalOperation.create("UPDATE", deviceInfo, "TreeKafkaConsumer");
			cacheIncrementalOperation(operation);
			log.info("树正在构建中，已缓存UPDATE操作: deviceId={}", deviceInfo.getDeviceId());
		}

		// 正常处理逻辑
		handleUpdateInternal(deviceInfo, null);
	}

	/**
	 * 内部UPDATE处理逻辑（用于正常处理和重放）
	 */
	private void handleUpdateInternal(DeviceInfo deviceInfo, String newVersion) {
		try {
			log.info("开始更新终端、监控对象信息: deviceInfo={}", deviceInfo);
			// 判断是终端、监控对象更新还是监控对象更新
			if (deviceInfo.getDeviceId() != null) {
				// 终端、监控对象信息更新
				handleDeviceUpdate(deviceInfo.getDeviceId(), newVersion);
			} else if (deviceInfo.getTargetId() != null) {
				// 监控对象信息更新（目前只支持名称更新）
				handleTargetUpdate(deviceInfo.getTargetId(), deviceInfo.getTargetName(), newVersion);
			} else {
				log.warn("终端、监控对象信息更新失败：deviceId和targetId都为空");
			}
		} catch (Exception e) {
			log.error("更新终端、监控对象信息失败: deviceInfo={}", deviceInfo, e);
		}
	}

	@Override
	public void handleAdd(DeviceInfo deviceInfo) {
		// 检查是否正在构建树
		if (isBuildingTree()) {
			// 缓存操作到队列
			IncrementalOperation operation = IncrementalOperation.create("ADD", deviceInfo, "TreeKafkaConsumer");
			cacheIncrementalOperation(operation);
			log.info("树正在构建中，已缓存ADD操作: deviceId={}", deviceInfo.getDeviceId());
		}

		// 正常处理逻辑
		handleAddInternal(deviceInfo, null);
	}

	/**
	 * 内部ADD处理逻辑（用于正常处理和重放）
	 */
	private void handleAddInternal(DeviceInfo deviceInfo, String newVersion) {
		try {
			log.info("开始新增终端、监控对象信息: deviceInfo={}", deviceInfo);

			// 判断是终端、监控对象新增还是监控对象新增
			if (deviceInfo.getDeviceId() != null) {
				// 终端、监控对象信息新增
				handleDeviceAdd(deviceInfo.getDeviceId(), null, newVersion);
			} else {
				log.info("非终端新增，跳过处理: deviceInfo={}", deviceInfo);
			}

		} catch (Exception e) {
			log.error("新增终端、监控对象信息失败: deviceInfo={}", deviceInfo, e);
		}
	}

	@Override
	public void handleDelete(DeviceInfo deviceInfo) {
		// 检查是否正在构建树
		if (isBuildingTree()) {
			IncrementalOperation operation = IncrementalOperation.create("DELETE", deviceInfo, "TreeKafkaConsumer");
			cacheIncrementalOperation(operation);
			log.info("树正在构建中，已缓存DELETE操作: deviceInfo={}", deviceInfo);
		}

		// 正常处理逻辑
		handleDeleteInternal(deviceInfo, null);
	}

	/**
	 * 内部DELETE处理逻辑（用于正常处理和重放）
	 */
	private void handleDeleteInternal(DeviceInfo deviceInfo, String newVersion) {
		try {
			log.info("开始删除终端、监控对象信息: deviceInfo={}", deviceInfo);

			// 判断是终端、监控对象删除还是监控对象删除
			if (CollUtil.isNotEmpty(deviceInfo.getDeviceIds())) {
				// 终端信息删除
				for (Long deviceId : deviceInfo.getDeviceIds()) {
					handleDeviceDelete(deviceId, newVersion);
				}
			} else if (CollUtil.isNotEmpty(deviceInfo.getTargetIds())) {

				Set<Long> targetIds = deviceInfo.getTargetIds();
				List<DeviceNode> deviceNodes = queryDeviceNodeByTargetIdIndex(targetIds, newVersion);
				if (CollUtil.isNotEmpty(deviceNodes)) {
					for (DeviceNode deviceNode : deviceNodes) {
						DeviceNode oldDeviceNode = handleDeviceDelete(deviceNode.getDeviceId(), newVersion);
						handleDeviceAdd(deviceNode.getDeviceId(), oldDeviceNode, newVersion);
					}
				}
			}

			log.info("终端、监控对象删除完成");
		} catch (Exception e) {
			log.error("删除终端、监控对象信息失败: deviceInfo={}", deviceInfo, e);
		}
	}

	/**
	 * 根据targetIds查询redis索引获取数据
	 *
	 * @param targetIds  监控对象ID集合
	 * @param newVersion
	 * @return DeviceNode列表
	 */
	private List<DeviceNode> queryDeviceNodeByTargetIdIndex(Set<Long> targetIds, String newVersion) {
		if (targetIds == null || targetIds.isEmpty()) {
			log.warn("targetIds为空，返回空列表");
			return Collections.emptyList();
		}

		try {
			// 获取RediSearch实例，使用当前版本的索引
			String currentVersion = getCurrentVersion(newVersion);
			String indexName = "tree_node_" + currentVersion + "_idx";
			RediSearch rediSearch = rediSearchClient.getRediSearch(indexName);

			// 构建查询语句：查找包含指定targetId的节点
			// 使用OR语法查询多个targetId: @targetId:{id1|id2|id3}
			String targetIdFilter = targetIds.stream()
				.map(String::valueOf)
				.collect(Collectors.joining("|"));
			String query = "@targetId:{" + targetIdFilter + "}";

			log.debug("查询Redis索引: indexName={}, query={}", indexName, query);

			// 设置搜索选项：返回所有匹配的结果
			SearchOptions options = new SearchOptions()
				.page(new io.github.dengliming.redismodule.redisearch.search.Page(0, targetIds.size() * 10)); // 预估每个target可能有多个device

			// 执行搜索
			SearchResult searchResult = rediSearch.search(query, options);

			if (searchResult == null || searchResult.getTotal() == 0 || searchResult.getDocuments().isEmpty()) {
				log.info("未找到匹配的DeviceNode: targetIds={}", targetIds);
				return Collections.emptyList();
			}

			// 转换搜索结果为DeviceNode列表
			List<DeviceNode> deviceNodes = new ArrayList<>();
			for (Document doc : searchResult.getDocuments()) {
				try {
					Map<String, Object> nodeData = doc.getFields();
					DeviceNode deviceNode = objectMapper.convertValue(nodeData, DeviceNode.class);
					deviceNodes.add(deviceNode);
				} catch (Exception e) {
					log.error("转换搜索结果为DeviceNode失败: doc={}", doc.getFields(), e);
				}
			}

			log.info("成功查询到 {} 个DeviceNode，targetIds={}", deviceNodes.size(), targetIds);
			return deviceNodes;

		} catch (Exception e) {
			log.error("使用索引查询DeviceNode失败: targetIds={}", targetIds, e);
			return Collections.emptyList();
		}
	}

	@Override
	public void handleDeviceBind(DeviceInfo deviceInfo) {
		// 检查是否正在构建树
		if (isBuildingTree()) {
			IncrementalOperation operation = IncrementalOperation.create("BIND", deviceInfo, "TreeKafkaConsumer");
			cacheIncrementalOperation(operation);
			log.info("树正在构建中，已缓存BIND操作: deviceId={}", deviceInfo.getDeviceId());
		}

		// 正常处理逻辑
		handleDeviceBindInternal(deviceInfo, null);
	}

	/**
	 * 内部BIND处理逻辑（用于正常处理和重放）
	 */
	private void handleDeviceBindInternal(DeviceInfo deviceInfo, String newVersion) {
		try {
			log.info("处理终端绑定: deviceId={}, targetId={}", deviceInfo.getDeviceId(), deviceInfo.getTargetId());
			for (Long deviceId : deviceInfo.getDeviceIds()) {

				deleteAndAdd(deviceId, newVersion);
			}

			log.info("终端绑定处理完成");
		} catch (Exception e) {
			log.error("处理终端绑定失败: deviceInfo={}", deviceInfo, e);
		}
	}

	@Override
	public void handleDeviceUnbind(DeviceInfo deviceInfo) {
		// 检查是否正在构建树
		if (isBuildingTree()) {
			IncrementalOperation operation = IncrementalOperation.create("UNBIND", deviceInfo, "TreeKafkaConsumer");
			cacheIncrementalOperation(operation);
			log.info("树正在构建中，已缓存UNBIND操作: deviceId={}", deviceInfo.getDeviceId());
		}

		// 正常处理逻辑
		handleDeviceUnbindInternal(deviceInfo, null);
	}

	/**
	 * 内部UNBIND处理逻辑（用于正常处理和重放）
	 */
	private void handleDeviceUnbindInternal(DeviceInfo deviceInfo, String newVersion) {
		try {
			log.info("处理终端解绑: deviceId={}, targetId={}", deviceInfo.getDeviceId(), deviceInfo.getTargetId());
			deleteAndAdd(deviceInfo.getDeviceId(), newVersion);
			log.info("终端解绑处理完成");
		} catch (Exception e) {
			log.error("处理终端解绑失败: deviceInfo={}", deviceInfo, e);
		}
	}

	@Override
	public void handleImport(DeviceInfo deviceInfo) {
		// 检查是否正在构建树
		if (isBuildingTree()) {
			IncrementalOperation operation = IncrementalOperation.create("IMPORT", deviceInfo, "TreeKafkaConsumer");
			cacheIncrementalOperation(operation);
			log.info("树正在构建中，已缓存IMPORT操作: deviceInfo={}", deviceInfo);
		}

		// 正常处理逻辑
		handleImportInternal(deviceInfo, null);
	}

	/**
	 * 内部IMPORT处理逻辑（用于正常处理和重放）
	 */
	private void handleImportInternal(DeviceInfo deviceInfo, String newVersion) {
		try {
			log.info("处理终端、监控对象导入: deviceId={}, targetId={}", deviceInfo.getDeviceId(), deviceInfo.getTargetId());

			// 判断是终端、监控对象删除还是监控对象删除
			if (CollUtil.isNotEmpty(deviceInfo.getDeviceIds())) {
				// 终端信息删除
				for (Long deviceId : deviceInfo.getDeviceIds()) {
					deleteAndAdd(deviceId, newVersion);
				}
			} else {
				log.info("非终端导入，跳过处理");
			}
			log.info("终端、监控对象导入处理完成");

		} catch (Exception e) {
			log.error("处理终端、监控对象导入失败: deviceInfo={}", deviceInfo, e);
		}
	}


	private DeviceNode handleDeviceDelete(Long deviceId, String newVersion) {

		String currentVersion = getCurrentVersion(newVersion);
		// 1. 查询终端节点信息
		DeviceNode deviceNode = queryDeviceNodeByIndex(deviceId, newVersion);
		if (deviceNode == null) {
			log.warn("终端节点不存在: deviceId={}", deviceId);
			return null;
		}

		// 2. 判断删除模式
		DeleteMode deleteMode = determineDeleteMode(currentVersion, deviceNode);

		// 3. 执行删除
		boolean deleteSuccess = executeDeviceDelete(currentVersion, deviceNode, deleteMode);

		// 4. 更新统计数据
		if (deleteSuccess) {
			updateStatisticsAfterDelete(currentVersion, deviceNode);

			// 5. 清理空节点
			cleanupEmptyNodes(currentVersion, deviceNode);
		}

		return deviceNode;

	}


	/**
	 * 删除模式枚举
	 */
	private enum DeleteMode {
		SINGLE_DEVICE,    // 单终端模式：删除整个监控对象节点
		MULTIPLE_DEVICE,  // 多终端模式：只删除终端节点
		CONVERT_TO_SINGLE // 多终端转单终端：删除终端节点并转换模式
	}

	/**
	 * 判断删除模式
	 *
	 * @param currentVersion 当前版本
	 * @param deviceNode     终端节点
	 * @return 删除模式
	 */
	private DeleteMode determineDeleteMode(String currentVersion, DeviceNode deviceNode) {
		try {
			// 检查监控对象是否包含终端属性（单终端模式）
			if (NodeType.TARGET.equals(deviceNode.getType())) {
				return DeleteMode.SINGLE_DEVICE;
			}

			// 检查监控对象下的终端数量
			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceNode));
			String childrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, targetNodeId);
			Long childrenCount = treeRedisTemplate.opsForZSet().count(childrenKey, Double.NEGATIVE_INFINITY, Double.POSITIVE_INFINITY);

			if (childrenCount != null && childrenCount == 2) {
				// 删除后只剩1个终端，需要转换为单终端模式
				return DeleteMode.CONVERT_TO_SINGLE;
			} else if (childrenCount != null && childrenCount > 2) {
				// 删除后还有多个终端，保持多终端模式
				return DeleteMode.MULTIPLE_DEVICE;
			} else {
				// 默认为多终端模式
				return DeleteMode.MULTIPLE_DEVICE;
			}
		} catch (Exception e) {
			log.error("判断删除模式失败: deviceId={}", deviceNode.getDeviceId(), e);
			return DeleteMode.MULTIPLE_DEVICE;
		}
	}

	@NotNull
	private static String getTargetNodeId(DeviceNode deviceNode) {
		return deviceNode.getDeptId() + "_" + deviceNode.getDeviceType() + "_" + deviceNode.getTargetId();
	}

	/**
	 * 执行终端删除
	 *
	 * @param currentVersion 当前版本
	 * @param deviceNode     终端节点
	 * @param deleteMode     删除模式
	 * @return 是否成功
	 */
	private boolean executeDeviceDelete(String currentVersion, DeviceNode deviceNode, DeleteMode deleteMode) {
		try {
			String deviceNodeId = TreeCacheKeyConstant.getPrefixedDeviceId(deviceNode.getDeviceId());
			String versionedDeviceKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceNodeId);

			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceNode));
			String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNodeId);

			switch (deleteMode) {
				case SINGLE_DEVICE:
					// 删除整个监控对象节点
					treeRedisTemplate.delete(versionedTargetKey);

					// 从终端类型的children中移除监控对象
					String deviceTypeNodeId = TreeCacheKeyConstant.getPrefixedDeviceTypeId(
						deviceNode.getDeptId(), deviceNode.getDeviceType());
					String deviceTypeChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, deviceTypeNodeId);
					treeRedisTemplate.opsForZSet().remove(deviceTypeChildrenKey, versionedTargetKey);

					log.info("单终端模式删除完成: deviceId={}, targetId={}", deviceNode.getDeviceId(), deviceNode.getTargetId());
					break;

				case MULTIPLE_DEVICE:
					// 只删除终端节点
					treeRedisTemplate.delete(versionedDeviceKey);

					// 从监控对象的children中移除终端
					String targetChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, targetNodeId);
					treeRedisTemplate.opsForZSet().remove(targetChildrenKey, versionedDeviceKey);

					log.info("多终端模式删除完成: deviceId={}", deviceNode.getDeviceId());
					break;

				case CONVERT_TO_SINGLE:
					// 删除终端节点
					treeRedisTemplate.delete(versionedDeviceKey);

					// 从监控对象的children中移除终端
					String childrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, targetNodeId);
					treeRedisTemplate.opsForZSet().remove(childrenKey, deviceNodeId);

					// 转换为单终端模式
					convertToSingleDeviceMode(currentVersion, targetNodeId);

					log.info("多终端转单终端模式删除完成: deviceId={}, targetId={}", deviceNode.getDeviceId(), deviceNode.getTargetId());
					break;

				default:
					log.error("未知的删除模式: {}", deleteMode);
					return false;
			}

			return true;
		} catch (Exception e) {
			log.error("执行终端删除失败: deviceId={}, deleteMode={}", deviceNode.getDeviceId(), deleteMode, e);
			return false;
		}
	}

	/**
	 * 转换为单终端模式
	 *
	 * @param currentVersion 当前版本
	 * @param targetKey      监控对象key
	 */
	private void convertToSingleDeviceMode(String currentVersion, String targetKey) {
		try {
			String childrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, targetKey);

			// 获取剩余的终端节点
			Set<String> remainingDevices = treeRedisTemplate.opsForZSet().range(childrenKey, 0, -1);

			if (remainingDevices != null && remainingDevices.size() == 1) {
				String remainingDeviceKey = remainingDevices.iterator().next();

				String versionedDeviceKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, remainingDeviceKey);
				// 获取剩余终端的数据
				Map<Object, Object> deviceData = treeRedisTemplate.opsForHash().entries(versionedDeviceKey);

				if (!deviceData.isEmpty()) {
					// 将终端属性复制到监控对象节点
					String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetKey);

					Map<String, String> targetUpdates = new HashMap<>();
					for (Map.Entry<Object, Object> entry : deviceData.entrySet()) {
						String field = entry.getKey().toString();
						String value = entry.getValue().toString();

						if (LambdaUtil.getFieldName(DeviceNode::getDeviceId).equals(field)) {
							targetUpdates.put(LambdaUtil.getFieldName(DeviceNode::getDeviceId), value);
						} else if (
							!LambdaUtil.getFieldName(DeviceNode::getTargetId).equals(field)
								&& !LambdaUtil.getFieldName(DeviceNode::getParentId).equals(field)
								&& !LambdaUtil.getFieldName(DeviceNode::getType).equals(field)
								&& !LambdaUtil.getFieldName(DeviceNode::getName).equals(field)
						) {
							targetUpdates.put(field, value);
						}
					}

					// 批量更新监控对象节点
					treeRedisTemplate.opsForHash().putAll(versionedTargetKey, targetUpdates);

					// 删除剩余的终端节点
					treeRedisTemplate.delete(versionedDeviceKey);

					// 清空监控对象的children
					treeRedisTemplate.delete(childrenKey);

					log.info("成功转换为单终端模式: targetKey={}", targetKey);
				}
			}
		} catch (Exception e) {
			log.error("转换为单终端模式失败: targetKey={}", targetKey, e);
		}
	}

	/**
	 * 删除后更新统计数据
	 *
	 * @param currentVersion 当前版本
	 * @param deviceNode     被删除的终端节点
	 */
	private void updateStatisticsAfterDelete(String currentVersion, DeviceNode deviceNode) {
		try {
			long totalDecrement = -1L;
			long onlineDecrement = (deviceNode.getOnline() != null && deviceNode.getOnline().equals(DeviceStatusConstant.ONLINE)) ? -1L : 0L;

			// 更新监控对象统计（如果还存在）
			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceNode));
			String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNodeId);
			if (treeRedisTemplate.hasKey(versionedTargetKey)) {
				updateNodeStatisticsInRedis(versionedTargetKey, totalDecrement, onlineDecrement);
			}

			// 更新终端类型统计
			String deviceTypeNodeId = TreeCacheKeyConstant.getPrefixedDeviceTypeId(
				deviceNode.getDeptId(), deviceNode.getDeviceType());
			String versionedDeviceTypeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceTypeNodeId);
			updateNodeStatisticsInRedis(versionedDeviceTypeKey, totalDecrement, onlineDecrement);

			// 更新部门统计（递归向上更新）
			updateDeptStatisticsRecursively(currentVersion, deviceNode.getDeptId(), totalDecrement, onlineDecrement);

			log.debug("删除后统计数据更新完成: deviceId={}", deviceNode.getDeviceId());
		} catch (Exception e) {
			log.error("删除后更新统计数据失败: deviceId={}", deviceNode.getDeviceId(), e);
		}
	}

	/**
	 * 清理空节点
	 *
	 * @param currentVersion 当前版本
	 * @param deviceNode     被删除的终端节点
	 */
	private void cleanupEmptyNodes(String currentVersion, DeviceNode deviceNode) {
		try {
			// 检查并清理空的终端类型节点
			cleanupEmptyDeviceTypeNode(currentVersion, deviceNode.getDeptId(), deviceNode.getDeviceType());

			// 检查并清理空的部门节点
			String deptNodeId = TreeCacheKeyConstant.getPrefixedDeptId(deviceNode.getDeptId());
			cleanupEmptyDeptNodes(currentVersion, deptNodeId);

			log.debug("空节点清理完成: deviceId={}", deviceNode.getDeviceId());
		} catch (Exception e) {
			log.error("清理空节点失败: deviceId={}", deviceNode.getDeviceId(), e);
		}
	}

	/**
	 * 清理空的终端类型节点
	 *
	 * @param currentVersion 当前版本
	 * @param deptId         部门ID
	 * @param deviceType     终端类型
	 */
	private void cleanupEmptyDeviceTypeNode(String currentVersion, Long deptId, Integer deviceType) {
		try {
			String deviceTypeNodeId = TreeCacheKeyConstant.getPrefixedDeviceTypeId(deptId, deviceType);
			String versionedDeviceTypeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceTypeNodeId);

			// 检查终端类型节点的total统计
			String totalStr = (String) treeRedisTemplate.opsForHash().get(versionedDeviceTypeKey, "total");
			if ("0".equals(totalStr)) {
				// 删除终端类型节点
				treeRedisTemplate.delete(versionedDeviceTypeKey);

				// 从部门的children中移除终端类型
				String deptNodeId = TreeCacheKeyConstant.getPrefixedDeptId(deptId);
				String deptChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, deptNodeId);
				treeRedisTemplate.opsForZSet().remove(deptChildrenKey, versionedDeviceTypeKey);

				log.info("清理空的终端类型节点: deptId={}, deviceType={}", deptId, deviceType);
			}
		} catch (Exception e) {
			log.error("清理空的终端类型节点失败: deptId={}, deviceType={}", deptId, deviceType, e);
		}
	}

	/**
	 * 递归清理空的部门节点
	 *
	 * @param currentVersion 当前版本
	 * @param deptNodeId     部门ID
	 */
	private void cleanupEmptyDeptNodes(String currentVersion, String deptNodeId) {
		try {
			if (StrUtil.isEmpty(deptNodeId)) {
				return;
			}

			String versionedDeptKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deptNodeId);

			// 检查部门节点的total统计
			String totalStr = (String) treeRedisTemplate.opsForHash().get(versionedDeptKey, "total");
			if ("0".equals(totalStr)) {
				String deptChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, deptNodeId);

				// 获取父部门节点ID
				String parentNodeId = (String) treeRedisTemplate.opsForHash().get(versionedDeptKey, "parentId");

				// 删除部门节点
				treeRedisTemplate.delete(versionedDeptKey);
				treeRedisTemplate.delete(deptChildrenKey);

				// 从父部门的children中移除当前部门
				if (parentNodeId != null && !TreeCacheKeyConstant.ROOT_DEPT_PARENT_ID.equals(parentNodeId)) {
					String parentChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, parentNodeId);
					treeRedisTemplate.opsForZSet().remove(parentChildrenKey, deptNodeId);

					// 递归检查父部门
					cleanupEmptyDeptNodes(currentVersion, parentNodeId);
				}

				log.info("清理空的部门节点: deptNodeId={}", parentNodeId);
			}
		} catch (Exception e) {
			log.error("清理空的部门节点失败: deptNodeId={}", deptNodeId, e);
		}
	}

	private boolean handleDeviceAdd(Long deviceId, DeviceNode oldDeviceNode, String newVersion) {
		try {
			log.info("开始处理终端新增: deviceId={}", deviceId);

			// 1. 获取新终端的完整信息
			TargetDeviceTreeDto deviceInfo = treeMapper.selectDeviceNodeByDeviceId(deviceId);
			if (deviceInfo == null) {
				log.warn("未找到终端信息: deviceId={}", deviceId);
				return false;
			}
			if (oldDeviceNode != null) {
				deviceInfo.setAcc(oldDeviceNode.getAcc());
				deviceInfo.setOnline(oldDeviceNode.getOnline());
				deviceInfo.setFusionState(oldDeviceNode.getFusionState());
			}

			log.info("获取到终端信息: deviceId={}, targetId={}, deptId={}, deviceType={}",
				deviceId, deviceInfo.getTargetId(), deviceInfo.getDeptId(), deviceInfo.getDeviceType());

			String currentVersion = getCurrentVersion(newVersion);

			// 2. 确保相关结构节点存在
			recursiveAddNewDept(currentVersion, deviceInfo.getDeptId());

			addNewDeviceType(currentVersion, deviceInfo.getDeptId(), deviceInfo.getDeviceType());

			// 3. 确保监控对象节点存在并处理终端添加逻辑
			boolean success = handleDeviceAddLogic(currentVersion, deviceInfo);

			if (success) {
				log.info("终端新增处理成功: deviceId={}", deviceId);
			} else {
				log.warn("终端新增处理失败: deviceId={}", deviceId);
			}

			return success;
		} catch (Exception e) {
			log.error("处理终端新增失败: deviceId={}", deviceId, e);
			return false;
		}
	}

	private boolean handleTargetUpdate(Long targetId, String newName, String newVersion) {
		try {
			if (newName == null || newName.trim().isEmpty()) {
				log.warn("监控对象名称更新失败：新名称为空, targetId={}", targetId);
				return false;
			}

			List<TargetNode> targetNodeList = queryTargetNodeByTargetId(targetId, newVersion);

			// 检查监控对象是否存在
			if (CollUtil.isEmpty(targetNodeList)) {
				log.warn("监控对象不存在: targetId={}", targetId);
				return false;
			}
			String currentVersion = getCurrentVersion(newVersion);

			// 更新名称
			for (TargetNode targetNode : targetNodeList) {
				String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNode.getId());
				treeRedisTemplate.opsForHash().put(versionedTargetKey, "name", newName);
			}

			log.info("监控对象名称更新成功: targetId={}, newName={}", targetId, newName);
			return true;
		} catch (Exception e) {
			log.error("更新监控对象名称失败: targetId={}, newName={}", targetId, newName, e);
			return false;
		}
	}

	/**
	 * 处理终端信息更新
	 */
	private boolean handleDeviceUpdate(Long deviceId, String newVersion) {
		try {
			TargetDeviceTreeDto deviceTreeDto = treeMapper.selectDeviceNodeByDeviceId(deviceId);

			DeviceNode deviceNode = queryDeviceNodeByIndex(deviceId, newVersion);
			if (deviceNode == null) {
				log.warn("通过索引未找到终端节点: deviceId={}", deviceId);
				return false;
			}
			String currentVersion = getCurrentVersion(newVersion);

			String versionedKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceNode.getId());
			// 如果部门相同，直接更新数据
			if (deviceTreeDto.getDeptId().equals(deviceNode.getDeptId())) {
				// 更新channelNum
				Integer newChannelNum = deviceTreeDto.getChannelNum();
				Integer currentChannelNum = deviceNode.getChannelNum();
				if (!Objects.equals(newChannelNum, currentChannelNum)) {
					treeRedisTemplate.opsForHash().put(versionedKey, LambdaUtil.getFieldName(DeviceNode::getChannelNum), newChannelNum.toString());
					log.info("更新channelNum: deviceId={}, {} -> {}", deviceId, currentChannelNum, newChannelNum);
				}
				// 更新UniqueId
				String newUniqueId = deviceTreeDto.getUniqueId();
				String currentUniqueId = deviceNode.getUniqueId();
				if (!Objects.equals(newUniqueId, currentUniqueId)) {
					treeRedisTemplate.opsForHash().put(versionedKey, LambdaUtil.getFieldName(DeviceNode::getUniqueId), newUniqueId);
					log.info("更新UniqueId: deviceId={}, {} -> {}", deviceId, currentUniqueId, newUniqueId);
				}

				// 更新category
				Integer newCategory = deviceTreeDto.getCategory();
				Integer currentCategory = deviceNode.getCategory();
				if (!Objects.equals(newCategory, currentCategory)) {
					treeRedisTemplate.opsForHash().put(versionedKey, LambdaUtil.getFieldName(DeviceNode::getCategory), newCategory.toString());
					log.info("更新category: deviceId={}, {} -> {}", deviceId, currentCategory, newCategory);
				}
			} else {
				deleteAndAdd(deviceId, null);
			}

			return true;
		} catch (Exception e) {
			log.error("处理终端信息更新失败: deviceId={}", deviceId, e);
			return false;
		}
	}


	private void deleteAndAdd(Long deviceId, String newVersion) {
		RLock lock = redissonClient.getLock(TreeCacheKeyConstant.BUILD_DEVICE_LOCK_KEY + deviceId);
		boolean isLock = false;
		try {
			isLock = lock.tryLock(10, TimeUnit.SECONDS);
			if (isLock) {
				// 先删除
				DeviceNode oldDeviceNode = handleDeviceDelete(deviceId, newVersion);
				// 重新添加
				handleDeviceAdd(deviceId, oldDeviceNode, newVersion);
			}

		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		} finally {
			if (isLock) {
				lock.unlock();
			}
		}
	}

	/**
	 * 添加新类型
	 *
	 * @param currentVersion
	 * @param newDeptId
	 * @param deviceType
	 */
	private void addNewDeviceType(String currentVersion, Long newDeptId, Integer deviceType) {
		String deviceTypeNodeId = TreeCacheKeyConstant.getPrefixedDeviceTypeId(newDeptId, deviceType);
		String versionedNodeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceTypeNodeId);
		Boolean hassedKey = treeRedisTemplate.hasKey(versionedNodeKey);
		if (!hassedKey) {
			R<String> dictR = dictBizClient.getValue(DictCodeConstant.DEVICE_TYPE, String.valueOf(deviceType));
			String dictTypeName = dictR.getData();
			if (StrUtil.isNotBlank(dictTypeName)) {
				DeviceTypeNode deviceTypeNode = new DeviceTypeNode();

				deviceTypeNode.setId(TreeCacheKeyConstant.getPrefixedDeviceTypeId(newDeptId, deviceType));
				deviceTypeNode.setDeptId(newDeptId);
				deviceTypeNode.setParentId(TreeCacheKeyConstant.getPrefixedDeptId(newDeptId));
				deviceTypeNode.setName(dictTypeName);
				deviceTypeNode.setType(NodeType.DEVICE_TYPE);
				deviceTypeNode.setTotal(0L);
				deviceTypeNode.setOnlineNum(0L);
				// 添加节点
				Map<String, String> nodeMap = convertNodeToStringMap(deviceTypeNode);
				treeRedisTemplate.opsForHash().putAll(versionedNodeKey, nodeMap);

				// 添加部门结构
				String parentKey = TreeCacheKeyConstant.getPrefixedDeptId(newDeptId);
				treeRedisTemplate.opsForZSet().add(
					TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, parentKey),
					deviceTypeNodeId,
					deviceType
				);
			}
		}
	}

	/**
	 * 递归添加新部门
	 *
	 * @param currentVersion
	 * @param newDeptId
	 */
	private void recursiveAddNewDept(String currentVersion, Long newDeptId) {
		String deptNodeId = TreeCacheKeyConstant.getPrefixedDeptId(newDeptId);
		String newDeptNodeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deptNodeId);
		Boolean hassedKey = treeRedisTemplate.hasKey(newDeptNodeKey);
		if (!hassedKey) {
			R<Dept> deptR = sysClient.getDept(newDeptId);
			Dept dept = deptR.getData();
			if (dept != null) {
				DeptNode deptNode = createDeptNode(dept);
				// 添加节点
				Map<String, String> nodeMap = convertNodeToStringMap(deptNode);

				treeRedisTemplate.opsForHash().putAll(newDeptNodeKey, nodeMap);
				// 添加结构
				treeRedisTemplate.opsForZSet().add(
					TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, deptNode.getParentId()),
					deptNodeId,
					deptNode.getDeptId()
				);
				recursiveAddNewDept(currentVersion, dept.getParentId());
			}
		}
	}

	/**
	 * 确保监控对象节点存在
	 *
	 * @param currentVersion 当前版本
	 * @param deviceInfo     终端信息
	 * @return
	 */
	private Long ensureTargetNodeExists(String currentVersion, TargetDeviceTreeDto deviceInfo) {
		String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceInfo));
		String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNodeId);
		HashOperations<String, String, String> opsForHash = treeRedisTemplate.opsForHash();
		if (!treeRedisTemplate.hasKey(versionedTargetKey)) {
			log.info("创建监控对象节点: targetId={}", deviceInfo.getTargetId());

			// 创建监控对象节点
			String deviceTypeNodeId = TreeCacheKeyConstant.getPrefixedDeviceTypeId(
				deviceInfo.getDeptId(), deviceInfo.getDeviceType());

			TargetNode targetNode = createTargetNode(deviceInfo);

			try {
				// 保存节点
				Map<String, String> nodeMap = convertNodeToStringMap(targetNode);
				opsForHash.putAll(versionedTargetKey, nodeMap);

				// 建立父子关系
				String parentChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(
					currentVersion, deviceTypeNodeId);
				treeRedisTemplate.opsForZSet().add(parentChildrenKey, targetNodeId,
					getScore(targetNode));
				log.info("监控对象节点创建成功: targetId={}", deviceInfo.getTargetId());
				return deviceInfo.getId();
			} catch (Exception e) {
				log.error("创建监控对象节点失败: targetId={}", deviceInfo.getTargetId(), e);
				throw new RuntimeException("创建监控对象节点失败", e);
			}
		} else {
			log.debug("监控对象节点已存在: targetId={}", deviceInfo.getTargetId());
			String deviceId = opsForHash.get(versionedTargetKey, LambdaUtil.getFieldName(DeviceNode::getDeviceId));
			if (StrUtil.isNotEmpty(deviceId)) {
				return Long.parseLong(deviceId);
			}
			return null;
		}
	}

	private Map<String, String> convertNodeToStringMap(BaseNode baseNode) {
		Map<String, Object> nodeMap = objectMapper.convertValue(baseNode, Map.class);

		Map<String, String> stringMap = new HashMap<>();
		nodeMap.forEach((k, v) -> {
			if (v != null) {
				stringMap.put(k, v.toString());
			}
		});
		return stringMap;
	}

	/**
	 * 处理终端添加逻辑（根据监控对象下的终端数量决定处理方式）
	 *
	 * @param currentVersion 当前版本
	 * @param deviceInfo     终端信息
	 * @return 是否成功
	 */
	private boolean handleDeviceAddLogic(String currentVersion, TargetDeviceTreeDto deviceInfo) {
		try {
			// 1. 确保监控对象节点存在
			Long deviceId = ensureTargetNodeExists(currentVersion, deviceInfo);

			if (deviceInfo.getId().equals(deviceId)) {
				// 单终端情况：将终端信息合并到监控对象节点中
				return handleSingleDeviceAdd(currentVersion, deviceInfo);
			} else {
				// 多终端情况：需要创建独立的终端节点
				return handleMultipleDeviceAdd(currentVersion, deviceInfo);
			}
		} catch (Exception e) {
			log.error("处理终端添加逻辑失败: deviceId={}", deviceInfo.getId(), e);
			return false;
		}
	}


	/**
	 * 处理单终端添加（将终端信息合并到监控对象节点）
	 *
	 * @param currentVersion 当前版本
	 * @param deviceTreeDto  终端信息
	 * @return 是否成功
	 */
	private boolean handleSingleDeviceAdd(String currentVersion, TargetDeviceTreeDto deviceTreeDto) {
		try {
			log.info("处理单终端添加: deviceId={}, targetId={}", deviceTreeDto.getId(), deviceTreeDto.getTargetId());

			// 获取监控对象节点

			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceTreeDto));
			String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNodeId);

			// 将终端属性设置到监控对象节点中
			setDevicePropertiesToTargetInRedis(versionedTargetKey, deviceTreeDto);

			// 更新统计数据
			updateDeviceStatistics(currentVersion, deviceTreeDto);

			log.info("单终端添加成功: deviceId={}, targetId={}", deviceTreeDto.getId(), deviceTreeDto.getTargetId());
			return true;
		} catch (Exception e) {
			log.error("处理单终端添加失败: deviceId={}", deviceTreeDto.getId(), e);
			return false;
		}
	}


	/**
	 * 处理多终端添加（创建独立的终端节点）
	 *
	 * @param currentVersion 当前版本
	 * @param deviceInfo     终端信息
	 * @return 是否成功
	 */
	private boolean handleMultipleDeviceAdd(String currentVersion, TargetDeviceTreeDto deviceInfo) {
		try {
			log.info("处理多终端添加: deviceId={}, targetId={}", deviceInfo.getId(), deviceInfo.getTargetId());

			// 检查是否需要将监控对象节点从单终端模式转换为多终端模式
			convertTargetToMultipleDeviceMode(currentVersion, deviceInfo);

			// 创建独立的终端节点
			return addDeviceNodeWithStatistics(currentVersion, deviceInfo);
		} catch (Exception e) {
			log.error("处理多终端添加失败: deviceId={}", deviceInfo.getId(), e);
			return false;
		}
	}

	/**
	 * 在Redis中设置终端属性到监控对象节点
	 *
	 * @param versionedTargetKey 版本化的监控对象Key
	 * @param deviceInfo         终端信息
	 */
	private void setDevicePropertiesToTargetInRedis(String versionedTargetKey, TargetDeviceTreeDto deviceInfo) {
		Map<String, String> deviceProperties = new HashMap<>();
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getDeviceId), deviceInfo.getId().toString());
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getOnline), deviceInfo.getOnline().toString());
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getFusionState), deviceInfo.getFusionState().toString());
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getUniqueId), deviceInfo.getUniqueId());
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getCreateAccount), deviceInfo.getCreateAccount());
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getCategory), deviceInfo.getCategory() != null ? deviceInfo.getCategory().toString() : "");
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getDeviceType), deviceInfo.getDeviceType() != null ? deviceInfo.getDeviceType().toString() : "");
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getAcc), deviceInfo.getAcc() != null ? deviceInfo.getAcc().toString() : "-1");
		deviceProperties.put(LambdaUtil.getFieldName(DeviceNode::getChannelNum), deviceInfo.getChannelNum() != null ? deviceInfo.getChannelNum().toString() : "0");

		treeRedisTemplate.opsForHash().putAll(versionedTargetKey, deviceProperties);
	}

	/**
	 * 使用Java代码原子性更新终端统计数据（替代Lua脚本）
	 *
	 * @param currentVersion 当前版本
	 * @param deviceInfo     终端信息
	 */
	private void updateDeviceStatistics(String currentVersion, TargetDeviceTreeDto deviceInfo) {
		try {
			// 计算增量
			long totalIncrement = 1L;
			long onlineIncrement = isDeviceOnline(deviceInfo) ? 1L : 0L;

			// 1. 更新监控对象统计
			updateTargetStatistics(currentVersion, deviceInfo, totalIncrement, onlineIncrement);

			// 2. 更新终端类型统计
			updateDeviceTypeStatistics(currentVersion, deviceInfo.getDeptId(), deviceInfo.getDeviceType(),
				totalIncrement, onlineIncrement);

			// 3. 递归更新部门统计
			updateDeptStatisticsRecursively(currentVersion, deviceInfo.getDeptId(), totalIncrement, onlineIncrement);

			log.debug("Java方式更新终端统计数据成功: deviceId={}, targetId={}, deptId={}, deviceType={}",
				deviceInfo.getId(), deviceInfo.getTargetId(), deviceInfo.getDeptId(), deviceInfo.getDeviceType());
		} catch (Exception e) {
			log.error("使用Java代码更新终端统计数据失败: deviceId={}", deviceInfo.getId(), e);
		}
	}

	/**
	 * 更新监控对象统计数据
	 *
	 * @param currentVersion  当前版本
	 * @param deviceTreeDto   监控对象
	 * @param totalIncrement  总数增量
	 * @param onlineIncrement 在线数增量
	 */
	private void updateTargetStatistics(String currentVersion, TargetDeviceTreeDto deviceTreeDto, long totalIncrement, long onlineIncrement) {
		Long targetId = deviceTreeDto.getTargetId();
		try {

			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceTreeDto));
			String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNodeId);

			// 检查监控对象节点是否存在
			if (treeRedisTemplate.hasKey(versionedTargetKey)) {
				updateNodeStatisticsInRedis(versionedTargetKey, totalIncrement, onlineIncrement);
				log.debug("更新监控对象统计成功: targetId={}, total+={}, online+={}",
					targetId, totalIncrement, onlineIncrement);
			} else {
				log.warn("监控对象节点不存在，跳过统计更新: targetId={}", targetId);
			}
		} catch (Exception e) {
			log.error("更新监控对象统计失败: targetId={}", targetId, e);
		}
	}

	/**
	 * 更新终端类型统计数据
	 *
	 * @param currentVersion  当前版本
	 * @param deptId          部门ID
	 * @param deviceType      终端类型
	 * @param totalIncrement  总数增量
	 * @param onlineIncrement 在线数增量
	 */
	private void updateDeviceTypeStatistics(String currentVersion, Long deptId, Integer deviceType,
											long totalIncrement, long onlineIncrement) {
		try {
			String deviceTypeNodeId = TreeCacheKeyConstant.getPrefixedDeviceTypeId(deptId, deviceType);
			String versionedDeviceTypeKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceTypeNodeId);

			// 检查终端类型节点是否存在
			if (treeRedisTemplate.hasKey(versionedDeviceTypeKey)) {
				updateNodeStatisticsInRedis(versionedDeviceTypeKey, totalIncrement, onlineIncrement);
				log.debug("更新终端类型统计成功: deptId={}, deviceType={}, total+={}, online+={}",
					deptId, deviceType, totalIncrement, onlineIncrement);
			} else {
				log.warn("终端类型节点不存在，跳过统计更新: deptId={}, deviceType={}", deptId, deviceType);
			}
		} catch (Exception e) {
			log.error("更新终端类型统计失败: deptId={}, deviceType={}", deptId, deviceType, e);
		}
	}


	/**
	 * 将监控对象节点从单终端模式转换为多终端模式
	 *
	 * @param currentVersion 当前版本
	 * @param deviceTreeDto  监控对象ID
	 */
	private void convertTargetToMultipleDeviceMode(String currentVersion, TargetDeviceTreeDto deviceTreeDto) {
		Long targetId = deviceTreeDto.getTargetId();
		try {
			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceTreeDto));
			String versionedTargetKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, targetNodeId);

			// 检查监控对象节点是否包含终端属性（单终端模式）
			String deviceIdStr = (String) treeRedisTemplate.opsForHash().get(versionedTargetKey, "deviceId");

			if (deviceIdStr != null) {
				log.info("将监控对象 {} 从单终端模式转换为多终端模式", targetId);

				// 获取原有的终端信息
				Map<Object, Object> targetData = treeRedisTemplate.opsForHash().entries(versionedTargetKey);
				TargetNode targetNode = objectMapper.convertValue(targetData, TargetNode.class);

				// 创建原有终端的独立节点
				createDeviceNodeFromTargetNode(currentVersion, targetNode);

				// 清除监控对象节点中的终端属性
				clearDevicePropertiesFromTarget(versionedTargetKey);

				log.info("监控对象 {} 转换为多终端模式完成", targetId);
			}
		} catch (Exception e) {
			log.error("转换监控对象为多终端模式失败: targetId={}", targetId, e);
		}
	}

	/**
	 * 从监控对象数据创建终端节点
	 *
	 * @param currentVersion 当前版本
	 * @param targetNode     监控对象数据
	 */
	private void createDeviceNodeFromTargetNode(String currentVersion, TargetNode targetNode) {
		try {
			Long deviceId = targetNode.getDeviceId();
			if (deviceId == null) {
				return;
			}

			String deviceNodeId = TreeCacheKeyConstant.getPrefixedDeviceId(deviceId);
			String versionedDeviceKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceNodeId);

			// 创建终端节点数据
			DeviceNode deviceNode = BeanUtil.copyProperties(targetNode, DeviceNode.class);
			deviceNode.setId(deviceNodeId);
			deviceNode.setParentId(targetNode.getId());
			deviceNode.setName(targetNode.getUniqueId());
			deviceNode.setType(NodeType.DEVICE);

			Map<String, String> deviceNodeData = convertNodeToStringMap(deviceNode);
			// 保存终端节点
			treeRedisTemplate.opsForHash().putAll(versionedDeviceKey, deviceNodeData);

			// 建立父子关系
			String parentChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, targetNode.getId());
			treeRedisTemplate.opsForZSet().add(parentChildrenKey, deviceNodeId, deviceId);

			log.info("从监控对象数据创建终端节点成功: deviceId={}", deviceId);
		} catch (Exception e) {
			log.error("从监控对象数据创建终端节点失败", e);
		}
	}

	/**
	 * 清除监控对象节点中的终端属性
	 *
	 * @param versionedTargetKey 版本化的监控对象Key
	 */
	private void clearDevicePropertiesFromTarget(String versionedTargetKey) {
		String[] deviceFields = {
			LambdaUtil.getFieldName(DeviceNode::getDeviceId),
			LambdaUtil.getFieldName(DeviceNode::getOnline),
			LambdaUtil.getFieldName(DeviceNode::getFusionState),
			LambdaUtil.getFieldName(DeviceNode::getUniqueId),
			LambdaUtil.getFieldName(DeviceNode::getCreateAccount),
			LambdaUtil.getFieldName(DeviceNode::getCategory),
			LambdaUtil.getFieldName(DeviceNode::getDeviceType),
			LambdaUtil.getFieldName(DeviceNode::getAcc),
			LambdaUtil.getFieldName(DeviceNode::getChannelNum)
		};
		treeRedisTemplate.opsForHash().delete(versionedTargetKey, (Object[]) deviceFields);
	}

	/**
	 * 添加终端节点并更新统计数据
	 *
	 * @param currentVersion 当前版本
	 * @param deviceTreeDto  终端信息
	 * @return 是否成功
	 */
	private boolean addDeviceNodeWithStatistics(String currentVersion, TargetDeviceTreeDto deviceTreeDto) {
		try {
			log.info("开始添加终端节点: deviceId={}", deviceTreeDto.getId());

			// 创建终端节点
			DeviceNode deviceNode = createDeviceNode(deviceTreeDto);
			String deviceKey = TreeCacheKeyConstant.getPrefixedDeviceId(deviceTreeDto.getId());
			String versionedDeviceKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deviceKey);

			// 保存终端节点
			Map<String, String> nodeMap = convertNodeToStringMap(deviceNode);
			treeRedisTemplate.opsForHash().putAll(versionedDeviceKey, nodeMap);

			// 建立父子关系
			String targetNodeId = TreeCacheKeyConstant.getPrefixedTargetId(getTargetNodeId(deviceNode));
			String parentChildrenKey = TreeCacheKeyConstant.getVersionedChildrenKey(currentVersion, targetNodeId);
			treeRedisTemplate.opsForZSet().add(parentChildrenKey, deviceKey, deviceTreeDto.getId());

			// 更新统计数据
			updateDeviceStatistics(currentVersion, deviceTreeDto);

			log.info("终端节点添加成功: deviceId={}", deviceTreeDto.getId());
			return true;
		} catch (Exception e) {
			log.error("添加终端节点失败: deviceId={}", deviceTreeDto.getId(), e);
			return false;
		}
	}

	/**
	 * 在Redis中更新节点统计数据
	 *
	 * @param versionedNodeKey 版本化的节点Key
	 * @param totalIncrement   总数增量
	 * @param onlineIncrement  在线数增量
	 */
	private void updateNodeStatisticsInRedis(String versionedNodeKey, long totalIncrement, long onlineIncrement) {
		try {
			treeRedisTemplate.opsForHash().increment(versionedNodeKey, "total", totalIncrement);
			treeRedisTemplate.opsForHash().increment(versionedNodeKey, "onlineNum", onlineIncrement);
		} catch (Exception e) {
			log.error("更新节点统计失败: nodeKey={}", versionedNodeKey, e);
		}
	}

	/**
	 * 递归更新部门统计数据（按照Lua脚本逻辑）
	 *
	 * @param currentVersion  当前版本
	 * @param deptId          部门ID
	 * @param totalIncrement  总数增量
	 * @param onlineIncrement 在线数增量
	 */
	private void updateDeptStatisticsRecursively(String currentVersion, Long deptId, long totalIncrement, long onlineIncrement) {
		if (deptId == null || deptId.equals(0L)) {
			return;
		}

		try {
			// 更新直属部门统计（包括selfTotal、selfOnlineNum、total、onlineNum）
			updateDirectDeptStatistics(currentVersion, deptId, totalIncrement, onlineIncrement);

			// 获取父部门ID并递归更新父部门（只更新total和onlineNum）
			String deptNodeId = TreeCacheKeyConstant.getPrefixedDeptId(deptId);
			String versionedDeptKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deptNodeId);
			String parentNodeId = (String) treeRedisTemplate.opsForHash().get(versionedDeptKey, "parentId");

			if (parentNodeId != null && !TreeCacheKeyConstant.ROOT_DEPT_PARENT_ID.equals(parentNodeId) && !parentNodeId.isEmpty()) {
				updateParentDeptStatistics(currentVersion, parentNodeId, totalIncrement, onlineIncrement);
			}
		} catch (Exception e) {
			log.error("递归更新部门统计失败: deptId={}", deptId, e);
		}
	}

	/**
	 * 更新直属部门统计数据（包括selfTotal、selfOnlineNum、total、onlineNum）
	 *
	 * @param currentVersion  当前版本
	 * @param deptId          部门ID
	 * @param totalIncrement  总数增量
	 * @param onlineIncrement 在线数增量
	 */
	private void updateDirectDeptStatistics(String currentVersion, Long deptId, long totalIncrement, long onlineIncrement) {
		try {
			String deptNodeId = TreeCacheKeyConstant.getPrefixedDeptId(deptId);
			String versionedDeptKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, deptNodeId);

			// 检查部门节点是否存在
			if (treeRedisTemplate.hasKey(versionedDeptKey)) {
				// 更新直属部门的selfTotal和selfOnlineNum
				treeRedisTemplate.opsForHash().increment(versionedDeptKey, "selfTotal", totalIncrement);
				treeRedisTemplate.opsForHash().increment(versionedDeptKey, "selfOnlineNum", onlineIncrement);

				// 更新部门的total和onlineNum（包含子部门统计）
				treeRedisTemplate.opsForHash().increment(versionedDeptKey, "total", totalIncrement);
				treeRedisTemplate.opsForHash().increment(versionedDeptKey, "onlineNum", onlineIncrement);

				log.debug("更新直属部门统计成功: deptId={}, selfTotal+={}, selfOnline+={}, total+={}, online+={}",
					deptId, totalIncrement, onlineIncrement, totalIncrement, onlineIncrement);
			} else {
				log.warn("直属部门节点不存在，跳过统计更新: deptId={}", deptId);
			}
		} catch (Exception e) {
			log.error("更新直属部门统计失败: deptId={}", deptId, e);
		}
	}

	/**
	 * 递归更新父部门统计数据（只更新total和onlineNum，不更新selfTotal和selfOnlineNum）
	 *
	 * @param currentVersion   当前版本
	 * @param parentDeptNodeId 父部门节点ID
	 * @param totalIncrement   总数增量
	 * @param onlineIncrement  在线数增量
	 */
	private void updateParentDeptStatistics(String currentVersion, String parentDeptNodeId, long totalIncrement, long onlineIncrement) {
		try {
			String versionedParentDeptKey = TreeCacheKeyConstant.getVersionedNodeKey(currentVersion, parentDeptNodeId);

			// 检查父部门节点是否存在
			if (treeRedisTemplate.hasKey(versionedParentDeptKey)) {
				// 只更新total和onlineNum（包含子部门统计）
				treeRedisTemplate.opsForHash().increment(versionedParentDeptKey, "total", totalIncrement);
				treeRedisTemplate.opsForHash().increment(versionedParentDeptKey, "onlineNum", onlineIncrement);

				log.debug("更新父部门统计成功: parentDeptNodeId={}, total+={}, online+={}",
					parentDeptNodeId, totalIncrement, onlineIncrement);

				// 获取上级部门ID并继续递归
				String grandParentIdStr = (String) treeRedisTemplate.opsForHash().get(versionedParentDeptKey, "parentId");
				if (grandParentIdStr != null && !TreeCacheKeyConstant.ROOT_DEPT_PARENT_ID.equals(grandParentIdStr) && !grandParentIdStr.isEmpty()) {
					updateParentDeptStatistics(currentVersion, grandParentIdStr, totalIncrement, onlineIncrement);
				}
			} else {
				log.warn("父部门节点不存在，跳过统计更新: parentDeptId={}", parentDeptNodeId);
			}
		} catch (Exception e) {
			log.error("更新父部门统计失败: parentDeptId={}", parentDeptNodeId, e);
		}
	}

	/**
	 * 使用Redis索引查询终端节点
	 *
	 * @param deviceId 终端ID
	 * @return 节点信息JSON字符串，如果未找到返回null
	 */
	@Override
	public DeviceNode queryDeviceNodeByIndex(Long deviceId) {
		return queryDeviceNodeByIndex(deviceId, null);
	}

	private DeviceNode queryDeviceNodeByIndex(Long deviceId, String newVersion) {
		try {
			String currentVersion = getCurrentVersion(newVersion);
			// 获取RediSearch实例，使用当前版本的索引
			String indexName = "tree_node_" + currentVersion + "_idx";
			RediSearch rediSearch = rediSearchClient.getRediSearch(indexName);

			// 构建查询语句：查找包含指定deviceId的节点
			String query = "@deviceId:{" + deviceId + "}";

			// 设置搜索选项：只返回第一个结果
			SearchOptions options = new SearchOptions()
				.page(new io.github.dengliming.redismodule.redisearch.search.Page(0, 1));

			// 执行搜索
			SearchResult searchResult = rediSearch.search(query, options);

			if (searchResult != null && searchResult.getTotal() > 0 && !searchResult.getDocuments().isEmpty()) {
				Document doc = searchResult.getDocuments().get(0);
				Map<String, Object> nodeData = doc.getFields();
				// 转成DeviceNode
				return objectMapper.convertValue(nodeData, DeviceNode.class);
			}
			return null;
		} catch (Exception e) {
			log.error("使用索引查询终端节点失败: deviceId={}", deviceId, e);
			return null;
		}
	}

	@Override
	public boolean updateDeviceState(DeviceNode deviceNode, String fieldName, Integer newValue) {
		try {
			// 始终更新当前版本，保证实时性
			boolean success = updateDeviceStateNormal(deviceNode, fieldName, newValue);

			// 如果正在构建树，缓存状态更新
			if (isBuildingTree()) {
				cacheDeviceStatusUpdate(deviceNode, fieldName, newValue);
				log.debug("树构建中，已缓存状态更新: deviceId={}, field={}, value={}",
					deviceNode.getDeviceId(), fieldName, newValue);
			}

			return success;

		} catch (Exception e) {
			log.error("更新终端{}失败: deviceId={}, {}={}", fieldName, deviceNode.getDeviceId(), fieldName, newValue, e);
			return false;
		}
	}

	/**
	 * 正常情况下的设备状态更新
	 */
	private boolean updateDeviceStateNormal(DeviceNode deviceNode, String fieldName, Integer newValue) {
		// 状态存储在终端节点中
		if (DeviceStatusInfo.FIELD_ONLINE.equals(fieldName)) {
			// 在线状态使用Lua脚本更新（包含统计逻辑）
			return updateNodeOnlineStatus(deviceNode, newValue);
		} else {
			// 其他字段直接更新
			return updateField(deviceNode, fieldName, newValue.toString());
		}
	}

	/**
	 * 使用Redis索引查询终端节点
	 *
	 * @param targetId   监控对象id
	 * @param newVersion
	 * @return 节点信息JSON字符串，如果未找到返回null
	 */
	private List<TargetNode> queryTargetNodeByTargetId(Long targetId, String newVersion) {
		// 获取RediSearch实例，使用当前版本的索引
		String currentVersion = getCurrentVersion(newVersion);
		String indexName = "tree_node_" + currentVersion + "_idx";
		RediSearch rediSearch = rediSearchClient.getRediSearch(indexName);

		// 构建查询语句：查找包含指定deviceId的节点
		String query = "@targetId:{" + targetId + "}";

		// 设置搜索选项：只返回第一个结果
		SearchOptions options = new SearchOptions()
			.page(new io.github.dengliming.redismodule.redisearch.search.Page(0, 1));

		// 执行搜索
		SearchResult searchResult = rediSearch.search(query, options);

		if (searchResult == null || searchResult.getTotal() == 0 || searchResult.getDocuments().isEmpty()) {
			log.info("未找到匹配的TargetNode: targetId={}", targetId);
			return Collections.emptyList();
		}

		// 转换搜索结果为DeviceNode列表
		List<TargetNode> deviceNodes = new ArrayList<>();
		for (Document doc : searchResult.getDocuments()) {
			try {
				Map<String, Object> nodeData = doc.getFields();
				TargetNode deviceNode = objectMapper.convertValue(nodeData, TargetNode.class);
				deviceNodes.add(deviceNode);
			} catch (Exception e) {
				log.error("转换搜索结果为TargetNode失败: doc={}", doc.getFields(), e);
			}
		}

		log.info("成功查询到 {} 个TargetNode，targetId={}", deviceNodes.size(), targetId);
		return deviceNodes;

	}

	// region 增量数据缓冲机制相关方法

	/**
	 * 设置构建状态
	 *
	 * @param building 是否正在构建
	 */
	private void setBuildingStatus(boolean building) {
		if (building) {
			treeRedisTemplate.opsForValue().set(
				TreeCacheKeyConstant.BUILD_STATUS_KEY,
				"true",
				TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES,
				TimeUnit.MINUTES
			);
			log.info("设置树构建状态: 正在构建");
		} else {
			treeRedisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
			log.info("清除树构建状态");
		}
	}

	/**
	 * 检查是否正在构建
	 */
	@Override
	public boolean isBuildingTree() {
		String status = treeRedisTemplate.opsForValue().get(TreeCacheKeyConstant.BUILD_STATUS_KEY);
		return "true".equals(status);
	}

	/**
	 * 缓存增量操作
	 */
	@Override
	public void cacheIncrementalOperation(IncrementalOperation operation) {
		try {
			String operationJson = JSON.toJSONString(operation);
			treeRedisTemplate.opsForList().rightPush(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, operationJson);
			// 设置队列过期时间，防止内存泄漏
			treeRedisTemplate.expire(
				TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY,
				TreeCacheKeyConstant.INCREMENTAL_QUEUE_EXPIRE_HOURS,
				TimeUnit.HOURS
			);
			log.info("缓存增量操作: type={}, deviceId={}, targetId={}",
				operation.getOperationType(),
				operation.getDeviceInfo() != null ? operation.getDeviceInfo().getDeviceId() : null,
				operation.getDeviceInfo() != null ? operation.getDeviceInfo().getTargetId() : null
			);
		} catch (Exception e) {
			log.error("缓存增量操作失败: operation={}", operation, e);
		}
	}

	@Override
	@SneakyThrows
	public List<BaseNodeVO> channel(BladeUser user) {

		// 显式指定结果序列化器为String
		final RedisSerializer<String> serializer = RedisSerializer.string();
		String jsonResult = treeRedisTemplate.execute(getTreeByCreatorScript, serializer, serializer, Collections.singletonList(user.getAccount()));

		if (jsonResult.isEmpty() || "[]".equals(jsonResult)) {
			return Collections.emptyList();
		}
		// Lua脚本返回的是一棵树
		List<BaseNodeVO> treeNodes = objectMapper.readValue(jsonResult, new com.fasterxml.jackson.core.type.TypeReference<List<BaseNodeVO>>() {
		});

		// 对树形结构进行节点合并和统计重新计算处理
		processTreeNodes(treeNodes);

		return treeNodes;
	}

	/**
	 * 重放增量操作到新版本
	 *
	 * @param newVersion 新版本号
	 */
	private void replayIncrementalOperations(String newVersion) {
		try {
			log.info("开始重放增量操作到新版本: {}", newVersion);

			List<String> operations = treeRedisTemplate.opsForList().range(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, 0, -1);
			if (CollectionUtils.isEmpty(operations)) {
				log.info("没有需要重放的增量操作");
				return;
			}

			log.info("发现 {} 个增量操作需要重放", operations.size());

			int successCount = 0;
			int failCount = 0;

			for (String operationJson : operations) {
				try {
					IncrementalOperation operation = JSON.parseObject(operationJson, IncrementalOperation.class);
					replayOperation(operation, newVersion);
					successCount++;
					log.debug("重放操作成功: type={}, id={}",
						operation.getOperationType(), operation.getOperationId());
				} catch (Exception e) {
					failCount++;
					log.error("重放单个增量操作失败: {}", operationJson, e);
				}
			}

			log.info("增量操作重放完成: 成功={}, 失败={}", successCount, failCount);


		} catch (Exception e) {
			log.error("重放增量操作失败", e);
			throw new RuntimeException("增量数据重放失败", e);
		}
	}

	/**
	 * 重放单个操作
	 *
	 * @param operation  增量操作
	 * @param newVersion
	 */
	private void replayOperation(IncrementalOperation operation, String newVersion) {
		DeviceInfo deviceInfo = operation.getDeviceInfo();
		try {
			switch (deviceInfo.getOperation()) {
				case INSERT:
					handleAddInternal(deviceInfo, newVersion);
					break;
				case UPDATE:
					handleUpdateInternal(deviceInfo, newVersion);
					break;
				case DELETE:
					handleDeleteInternal(deviceInfo, newVersion);
					break;
				case BIND:
					handleDeviceBindInternal(deviceInfo, newVersion);
					break;
				case UNBIND:
					handleDeviceUnbindInternal(deviceInfo, newVersion);
					break;
				case IMPORT:
					handleImportInternal(deviceInfo, newVersion);
					break;
				default:
					log.warn("未知的操作类型: {}", deviceInfo.getOperation());
			}
		} catch (Exception e) {
			log.error("重放操作失败: type={}, deviceId={}",
				deviceInfo.getOperation(),
				deviceInfo != null ? deviceInfo.getDeviceId() : null, e);
			throw e;
		}
	}

	/**
	 * 清理构建状态和队列
	 */
	private void cleanupBuildStatus() {
		try {
			treeRedisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
			treeRedisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);
			treeRedisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);
			log.info("构建状态清理完成");
		} catch (Exception e) {
			log.error("清理构建状态失败", e);
		}
	}

	/**
	 * 缓存设备状态更新（使用Hash结构自动去重）
	 *
	 * @param deviceNode 设备ID
	 * @param fieldName  字段名
	 * @param newValue   新值
	 */
	private void cacheDeviceStatusUpdate(DeviceNode deviceNode, String fieldName, Integer newValue) {
		Long deviceId = deviceNode.getDeviceId();

		try {
			String nodeId = deviceNode.getId();
			nodeId = nodeId + "##" + fieldName;
			treeRedisTemplate.opsForHash().put(
				TreeCacheKeyConstant.STATUS_CACHE_KEY,
				nodeId,
				newValue.toString()
			);

			// 设置缓存过期时间
			treeRedisTemplate.expire(
				TreeCacheKeyConstant.STATUS_CACHE_KEY,
				TreeCacheKeyConstant.INCREMENTAL_QUEUE_EXPIRE_HOURS,
				TimeUnit.HOURS
			);

			log.debug("缓存状态更新: deviceId={}, field={}, value={}", deviceId, fieldName, newValue);
		} catch (Exception e) {
			log.error("缓存状态更新失败: deviceId={}, field={}, value={}", deviceId, fieldName, newValue, e);
		}
	}

	/**
	 * 同步缓存的状态更新到新版本
	 *
	 * @param newVersion 新版本号
	 */
	private void syncCachedStatusToNewVersion(String newVersion) {
		try {
			log.info("开始同步缓存的状态更新到新版本: {}", newVersion);

			Map<Object, Object> cachedStatus = treeRedisTemplate.opsForHash()
				.entries(TreeCacheKeyConstant.STATUS_CACHE_KEY);

			if (cachedStatus.isEmpty()) {
				log.info("没有缓存的状态更新需要同步");
				return;
			}

			log.info("发现 {} 个缓存的状态更新需要同步", cachedStatus.size());

			int successCount = 0;
			int failCount = 0;

			for (Map.Entry<Object, Object> entry : cachedStatus.entrySet()) {
				try {
					String cacheKey = entry.getKey().toString();
					String value = entry.getValue().toString();

					// 解析缓存key：deviceId:fieldName
					String[] parts = cacheKey.split("##", 2);
					if (parts.length != 2) {
						log.warn("无效的缓存key格式: {}", cacheKey);
						failCount++;
						continue;
					}

					String nodekey = parts[0];
					String fieldName = parts[1];
					Integer newValue = Integer.valueOf(value);

					// 应用状态更新到新版本
					boolean success = applyStatusUpdateToNewVersion(nodekey, fieldName, newValue, newVersion);
					if (success) {
						successCount++;
						log.debug("状态同步成功: deviceId={}, field={}, value={}", nodekey, fieldName, newValue);
					} else {
						failCount++;
						log.warn("状态同步失败: deviceId={}, field={}, value={}", nodekey, fieldName, newValue);
					}

				} catch (Exception e) {
					failCount++;
					log.error("同步单个状态更新失败: key={}, value={}", entry.getKey(), entry.getValue(), e);
				}
			}

			log.info("状态同步完成: 成功={}, 失败={}", successCount, failCount);

			// 清理状态缓存
			treeRedisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);
			log.info("状态缓存清理完成");

		} catch (Exception e) {
			log.error("同步缓存状态到新版本失败", e);
		}
	}

	/**
	 * 将状态更新应用到新版本中的设备节点
	 *
	 * @param nodeKey   设备ID
	 * @param fieldName 字段名
	 * @param newValue  新值
	 * @param version   版本号
	 * @return 是否成功
	 */
	private boolean applyStatusUpdateToNewVersion(String nodeKey, String fieldName, Integer newValue, String version) {
		try {
			// 尝试作为设备节点更新
			String versionedDeviceKey = TreeCacheKeyConstant.getVersionedNodeKey(version, nodeKey);

			if (DeviceStatusInfo.FIELD_ONLINE.equals(fieldName)) {
				// 在线状态需要使用Lua脚本更新统计
				return updateOnlineStatusInVersion(versionedDeviceKey, newValue, version);
			} else {
				// 其他状态直接更新字段
				treeRedisTemplate.opsForHash().put(versionedDeviceKey, fieldName, newValue.toString());
				return true;
			}

		} catch (Exception e) {
			log.error("应用状态更新到新版本失败: nodeKey={}, field={}, version={}", nodeKey, fieldName, version, e);
			return false;
		}
	}

	/**
	 * 在指定版本中更新在线状态（使用Lua脚本）
	 */
	private boolean updateOnlineStatusInVersion(String versionedNodeKey, Integer onlineStatus, String version) {
		try {
			Long result = treeRedisTemplate.execute(
				updateDeviceStatusScript,
				Collections.singletonList(versionedNodeKey),
				String.valueOf(onlineStatus),
				version
			);
			return result > 0;
		} catch (Exception e) {
			log.error("在版本{}中更新在线状态失败: nodeKey={}", version, versionedNodeKey, e);
			return false;
		}
	}


}
