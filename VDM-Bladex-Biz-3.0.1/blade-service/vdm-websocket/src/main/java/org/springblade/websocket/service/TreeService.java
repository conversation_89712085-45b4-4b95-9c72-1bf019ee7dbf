package org.springblade.websocket.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.secure.BladeUser;
import org.springblade.websocket.dto.DeviceInfo;
import org.springblade.websocket.dto.TreeSearchData;
import org.springblade.websocket.dto.tree.DeviceNode;
import org.springblade.websocket.vo.tree.BaseNodeVO;
import org.springblade.websocket.vo.tree.TreeResponse;

import java.util.List;
import java.util.Set;

public interface TreeService {

	/**
	 * 构建完整的树缓存
	 */
	void buildTree(long lockSecond);


	/**
	 * search device node by keyword
	 *
	 * @param keyword  keyword
	 * @param current  current page
	 * @param pageSize page size
	 * @return Page<BaseNode>
	 */
	Page<TreeSearchData> searchNodes(String keyword, long current, long pageSize);


	/**
	 * 根据创建者ID获取其创建的终端树
	 * @param userId 创建者ID
	 * @return
	 */
	List<BaseNodeVO> getTreeByCreator(String userId);



	TreeResponse getDynamicTreeForWebsocket(String userId);

	/**
	 * 根据展开节点列表动态构建树结构
	 *
	 * @param expandedNodeIds 展开的节点ID列表
	 * @return 动态构建的树结构
	 */
	TreeResponse getDynamicTree(Set<String> expandedNodeIds);


	/**
	 * 更新设备信息（支持增量更新）
	 * @param deviceInfo 设备信息
	 * @return 更新是否成功
	 */
	void handleUpdate(DeviceInfo deviceInfo);

	/**
	 * 处理新增
	 * @param deviceInfo
	 * @return
	 */
	void handleAdd(DeviceInfo deviceInfo);

	/**
	 * 处理删除
	 * @param deviceInfo
	 * @return
	 */
	void handleDelete(DeviceInfo deviceInfo);

	/**
	 * 处理终端绑定
	 * @param deviceInfo
	 * @return
	 */
	void handleDeviceBind(DeviceInfo deviceInfo);

	/**
	 * 处理终端解绑
	 * @param deviceInfo
	 * @return
	 */
	void handleDeviceUnbind(DeviceInfo deviceInfo);

	/**
	 * 处理导入
	 * @param deviceInfo
	 * @return
	 */
	void handleImport(DeviceInfo deviceInfo);


	/**
	 * 根据索引查询
	 * @param deviceId
	 * @return
	 */
	DeviceNode queryDeviceNodeByIndex(Long deviceId);

	/**
	 *更新设备状态
	 * @param deviceNode
	 * @param fieldOnline
	 * @param newValue
	 * @return
	 */
	boolean updateDeviceState(DeviceNode deviceNode, String fieldOnline, Integer newValue);

	/**
	 * 检查是否正在构建树
	 * @return true-正在构建，false-未在构建
	 */
	boolean isBuildingTree();

	/**
	 * 缓存增量操作到队列
	 * @param operation 操作信息
	 */
	void cacheIncrementalOperation(org.springblade.websocket.dto.IncrementalOperation operation);

	/**
	 * 获取带通道的树
	 * @param user
	 * @return
	 */
	List<BaseNodeVO> channel(BladeUser user);
}
