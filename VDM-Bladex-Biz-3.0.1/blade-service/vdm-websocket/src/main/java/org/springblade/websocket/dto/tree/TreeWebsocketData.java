package org.springblade.websocket.dto.tree;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class TreeWebsocketData<T> {

	public static final String TYPE_ACC_CHANGE = "ACC_CHANGE";

	public static final String TYPE_FUSION_STATE_CHANGE = "FUSION_STATE_CHANGE";

	public static final String TYPE_TREE_CHANGE =  "TREE_CHANGE";

	/**
	 * 类型
	 */
	private String type;
	/**
	 * 数据
	 */
	private T data;
	/**
	 * 时间戳
	 */
	private Long timestamp;
}
